.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);

  .messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    scroll-behavior: smooth;

    .message-wrapper {
      display: flex;
      width: 100%;

      .message {
        max-width: 80%;
        word-wrap: break-word;

        &.human-message {
          margin-left: auto;

          .human-card {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;

            .message-content {
              font-size: 1rem;
              line-height: 1.5;
            }

            .message-time {
              font-size: 0.75rem;
              opacity: 0.8;
              margin-top: 0.5rem;
              text-align: right;
            }
          }
        }

        &.ai-message {
          margin-right: auto;

          .ai-card {
            background: rgba(48, 48, 48, 0.9);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.1);

            .ai-header {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              color: #e0e0e0;

              mat-icon {
                color: #4caf50;
              }

              .copy-button {
                margin-left: auto;
                color: #b0b0b0;

                &:hover {
                  color: #ffffff;
                  background: rgba(255, 255, 255, 0.1);
                }
              }
            }

            .message-content {
              font-size: 1rem;
              line-height: 1.6;
              margin: 1rem 0;
              white-space: pre-wrap;

              // Markdown styling to match React version
              ::ng-deep markdown {
                h1 {
                  font-size: 2rem;
                  font-weight: bold;
                  margin: 1rem 0 0.5rem 0;
                  color: #ffffff;
                }

                h2 {
                  font-size: 1.5rem;
                  font-weight: bold;
                  margin: 0.75rem 0 0.5rem 0;
                  color: #ffffff;
                }

                h3 {
                  font-size: 1.25rem;
                  font-weight: bold;
                  margin: 0.75rem 0 0.25rem 0;
                  color: #ffffff;
                }

                p {
                  margin-bottom: 0.75rem;
                  line-height: 1.7;
                  color: #e0e0e0;
                }

                a {
                  color: #2196f3;
                  text-decoration: none;
                  padding: 0.125rem 0.25rem;
                  background: rgba(33, 150, 243, 0.1);
                  border-radius: 0.25rem;
                  font-size: 0.875rem;
                  margin: 0 0.125rem;

                  &:hover {
                    color: #1976d2;
                    background: rgba(33, 150, 243, 0.2);
                  }
                }

                ul, ol {
                  margin: 0.75rem 0;
                  padding-left: 1.5rem;

                  li {
                    margin-bottom: 0.25rem;
                    color: #e0e0e0;
                  }
                }

                blockquote {
                  border-left: 4px solid #666666;
                  padding-left: 1rem;
                  font-style: italic;
                  margin: 0.75rem 0;
                  color: #b0b0b0;
                  font-size: 0.875rem;
                }

                code {
                  background: #1a1a1a;
                  border-radius: 0.25rem;
                  padding: 0.125rem 0.25rem;
                  font-family: 'Courier New', monospace;
                  font-size: 0.875rem;
                  color: #f0f0f0;
                }

                pre {
                  background: #1a1a1a;
                  padding: 0.75rem;
                  border-radius: 0.5rem;
                  overflow-x: auto;
                  font-family: 'Courier New', monospace;
                  font-size: 0.875rem;
                  margin: 0.75rem 0;
                  color: #f0f0f0;

                  code {
                    background: transparent;
                    padding: 0;
                  }
                }

                hr {
                  border: none;
                  border-top: 1px solid #666666;
                  margin: 1rem 0;
                }

                table {
                  border-collapse: collapse;
                  width: 100%;
                  margin: 0.75rem 0;
                  overflow-x: auto;
                  display: block;
                  white-space: nowrap;

                  th, td {
                    border: 1px solid #666666;
                    padding: 0.75rem;
                    text-align: left;
                  }

                  th {
                    font-weight: bold;
                    background: rgba(255, 255, 255, 0.05);
                  }
                }
              }
            }

            .message-time {
              font-size: 0.75rem;
              color: #b0b0b0;
              margin-top: 0.5rem;
            }

            .activity-section {
              margin-top: 1rem;
              border-top: 1px solid rgba(255, 255, 255, 0.1);
              padding-top: 1rem;
            }

            &.loading-card {
              .loading-content {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                color: #b0b0b0;

                .loading-icon {
                  color: #4caf50;
                  animation: pulse 2s infinite;
                }
              }
            }
          }
        }
      }
    }
  }

  .input-area {
    flex-shrink: 0;
    padding: 1rem;
    background: rgba(32, 32, 32, 0.9);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Responsive design
@media (max-width: 768px) {
  .chat-container {
    .messages-area {
      padding: 0.5rem;

      .message-wrapper {
        .message {
          max-width: 95%;

          &.human-message .human-card,
          &.ai-message .ai-card {
            .message-content {
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    .input-area {
      padding: 0.5rem;
    }
  }
}

// Scrollbar styling
.messages-area {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
