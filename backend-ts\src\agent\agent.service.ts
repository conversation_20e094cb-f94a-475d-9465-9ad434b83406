import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ConfigurationService } from './configuration.service';
import { 
  OverallState, 
  QueryGenerationState, 
  ReflectionState, 
  WebSearchState 
} from './state';
import { 
  getCurrentDate, 
  queryWriterInstructions, 
  webSearcherInstructions, 
  reflectionInstructions, 
  answerInstructions 
} from './prompts';
import { 
  getResearchTopic, 
  resolveUrls, 
  getCitations, 
  insertCitationMarkers 
} from './utils';
import { SearchQueryListClass, ReflectionClass } from './tools-and-schemas';
import { RunnableConfig } from './types';
import { AIMessage } from '@langchain/core/messages';

/**
 * Agent service that implements the LangGraph agent functionality.
 * Equivalent to the Python graph.py implementation.
 */
@Injectable()
export class AgentService {
  private genaiClient: any; // Will be replaced with actual Google Generative AI client

  constructor(
    private configService: ConfigService,
    private configurationService: ConfigurationService,
  ) {
    // Disable <PERSON>Smith tracing completely
    process.env.LANGCHAIN_TRACING_V2 = 'false';
    process.env.LANGCHAIN_API_KEY = '';

    // Check for GEMINI_API_KEY
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is not set');
    }

    // Initialize Google Generative AI client
    // Note: This is a placeholder. In a real implementation, you would use the actual Google Generative AI client
    this.genaiClient = {
      models: {
        generateContent: async (params: any) => {
          // This is a placeholder for the actual Google Generative AI client
          // In a real implementation, you would use the actual client to generate content
          return {
            candidates: [
              {
                text: 'Generated content',
                grounding_metadata: {
                  grounding_chunks: [],
                  grounding_supports: [],
                },
              },
            ],
          };
        },
      },
    };
  }

  /**
   * LangGraph node that generates search queries based on the User's question.
   * Equivalent to the Python generate_query function.
   */
  async generateQuery(state: OverallState, config: RunnableConfig): Promise<QueryGenerationState> {
    const configurable = ConfigurationService.fromRunnableConfig(config);

    // Check for custom initial search query count
    if (state.initial_search_query_count === undefined) {
      state.initial_search_query_count = configurable.numberOfInitialQueries;
    }

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = queryWriterInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{number_queries}', (state.initial_search_query_count ?? configurable.numberOfInitialQueries).toString());

    // Generate the search queries
    // Note: This is a placeholder. In a real implementation, you would use the actual Google Generative AI client
    // to generate structured output for search queries
    const result = new SearchQueryListClass({
      query: ['Sample query 1', 'Sample query 2', 'Sample query 3'],
      rationale: 'Sample rationale',
    });

    return { query_list: result.query.map((query: string) => ({ query, rationale: result.rationale })) };
  }

  /**
   * LangGraph node that sends the search queries to the web research node.
   * Equivalent to the Python continue_to_web_research function.
   */
  continueToWebResearch(state: QueryGenerationState): { target: string, state: WebSearchState }[] {
    return state.query_list.map((searchQuery, idx) => ({
      target: 'web_research',
      state: {
        search_query: searchQuery.query,
        id: idx.toString(),
      },
    }));
  }

  /**
   * LangGraph node that performs web research using the native Google Search API tool.
   * Equivalent to the Python web_research function.
   */
  async webResearch(state: WebSearchState, config: RunnableConfig): Promise<Partial<OverallState>> {
    // Configure
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const formattedPrompt = webSearcherInstructions
      .replace('{current_date}', getCurrentDate())
      .replace('{research_topic}', state.search_query);

    // Uses the google genai client as the langchain client doesn't return grounding metadata
    // Note: This is a placeholder. In a real implementation, you would use the actual Google Generative AI client
    const response = await this.genaiClient.models.generateContent({
      model: configurable.queryGeneratorModel,
      contents: formattedPrompt,
      config: {
        tools: [{ google_search: {} }],
        temperature: 0,
      },
    });

    // Resolve the urls to short urls for saving tokens and time
    const resolvedUrls = resolveUrls(
      response.candidates[0].grounding_metadata.grounding_chunks,
      parseInt(state.id)
    );

    // Gets the citations and adds them to the generated text
    const citations = getCitations(response, resolvedUrls);
    const modifiedText = insertCitationMarkers(response.text, citations);
    const sourcesGathered = citations.flatMap(citation => citation.segments);

    return {
      sources_gathered: sourcesGathered,
      search_query: [state.search_query],
      web_research_result: [modifiedText],
    };
  }

  /**
   * LangGraph node that identifies knowledge gaps and generates potential follow-up queries.
   * Equivalent to the Python reflection function.
   */
  async reflection(state: OverallState, config: RunnableConfig): Promise<ReflectionState> {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    
    // Increment the research loop count and get the reasoning model
    state.research_loop_count = (state.research_loop_count || 0) + 1;
    const reasoningModel = state.reasoning_model || configurable.reflectionModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = reflectionInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n\n---\n\n'));

    // Note: This is a placeholder. In a real implementation, you would use the actual Google Generative AI client
    // to generate structured output for reflection
    const result = new ReflectionClass({
      is_sufficient: false,
      knowledge_gap: 'Sample knowledge gap',
      follow_up_queries: ['Sample follow-up query 1', 'Sample follow-up query 2'],
    });

    return {
      is_sufficient: result.is_sufficient,
      knowledge_gap: result.knowledge_gap,
      follow_up_queries: result.follow_up_queries,
      research_loop_count: state.research_loop_count,
      number_of_ran_queries: state.search_query.length,
    };
  }

  /**
   * LangGraph routing function that determines the next step in the research flow.
   * Equivalent to the Python evaluate_research function.
   */
  evaluateResearch(
    state: ReflectionState,
    config: RunnableConfig,
  ): string | { target: string, state: WebSearchState }[] {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const maxResearchLoops = state.research_loop_count !== undefined
      ? state.research_loop_count
      : configurable.maxResearchLoops;

    if (state.is_sufficient || state.research_loop_count >= maxResearchLoops) {
      return 'finalize_answer';
    } else {
      return state.follow_up_queries.map((followUpQuery, idx) => ({
        target: 'web_research',
        state: {
          search_query: followUpQuery,
          id: (state.number_of_ran_queries + idx).toString(),
        },
      }));
    }
  }

  /**
   * LangGraph node that finalizes the research summary.
   * Equivalent to the Python finalize_answer function.
   */
  async finalizeAnswer(state: OverallState, config: RunnableConfig): Promise<Partial<OverallState>> {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const reasoningModel = state.reasoning_model || configurable.answerModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = answerInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n---\n\n'));

    // Note: This is a placeholder. In a real implementation, you would use the actual Google Generative AI client
    const result = {
      content: 'Sample answer with [citation](https://vertexaisearch.cloud.google.com/id/0-0)',
    };

    // Replace the short urls with the original urls and add all used urls to the sources_gathered
    const uniqueSources = [];
    for (const source of state.sources_gathered) {
      if (result.content.includes(source.short_url)) {
        result.content = result.content.replace(source.short_url, source.value);
        uniqueSources.push(source);
      }
    }

    return {
      messages: [new AIMessage({ content: result.content })],
      sources_gathered: uniqueSources,
    };
  }

  /**
   * Create and run the agent graph.
   * This is a simplified version of the Python graph creation and execution.
   */
  async runAgent(initialState: OverallState): Promise<OverallState> {
    // This is a placeholder for the actual LangGraph implementation
    // In a real implementation, you would use the TypeScript equivalent of LangGraph
    // to create and run the agent graph

    // For now, we'll simulate the agent graph execution with a simple sequential flow
    let state = { ...initialState };
    
    // Generate query
    const queryState = await this.generateQuery(state, {});
    
    // Continue to web research
    const webResearchTasks = this.continueToWebResearch(queryState);
    
    // Execute web research tasks
    const webResearchResults = await Promise.all(
      webResearchTasks.map(task => this.webResearch(task.state, {}))
    );
    
    // Merge web research results into state
    webResearchResults.forEach(result => {
      state.sources_gathered = [...(state.sources_gathered || []), ...(result.sources_gathered || [])];
      state.search_query = [...(state.search_query || []), ...(result.search_query || [])];
      state.web_research_result = [...(state.web_research_result || []), ...(result.web_research_result || [])];
    });
    
    // Reflection
    const reflectionState = await this.reflection(state, {});
    
    // Evaluate research
    const evaluationResult = this.evaluateResearch(reflectionState, {});
    
    // If evaluation result is a string, finalize answer
    if (typeof evaluationResult === 'string') {
      const finalResult = await this.finalizeAnswer(state, {});
      state = { ...state, ...finalResult };
    } else {
      // Execute follow-up web research tasks
      const followUpResults = await Promise.all(
        evaluationResult.map(task => this.webResearch(task.state, {}))
      );
      
      // Merge follow-up results into state
      followUpResults.forEach(result => {
        state.sources_gathered = [...(state.sources_gathered || []), ...(result.sources_gathered || [])];
        state.search_query = [...(state.search_query || []), ...(result.search_query || [])];
        state.web_research_result = [...(state.web_research_result || []), ...(result.web_research_result || [])];
      });
      
      // Final reflection and answer
      const finalReflectionState = await this.reflection(state, {});
      const finalEvaluationResult = this.evaluateResearch(finalReflectionState, {});
      
      if (typeof finalEvaluationResult === 'string') {
        const finalResult = await this.finalizeAnswer(state, {});
        state = { ...state, ...finalResult };
      }
    }
    
    return state;
  }
}
