import { z } from 'zod';

/**
 * Search query list schema using Zod for structured output.
 * Equivalent to the Python SearchQueryList class.
 */
export const SearchQueryListSchema = z.object({
  query: z.array(z.string()).describe('A list of search queries to be used for web research.'),
  rationale: z.string().describe('A brief explanation of why these queries are relevant to the research topic.'),
});

export type SearchQueryList = z.infer<typeof SearchQueryListSchema>;

/**
 * Reflection schema using Zod for structured output.
 * Equivalent to the Python Reflection class.
 */
export const ReflectionSchema = z.object({
  is_sufficient: z.boolean().describe('Whether the provided summaries are sufficient to answer the user\'s question.'),
  knowledge_gap: z.string().describe('A description of what information is missing or needs clarification.'),
  follow_up_queries: z.array(z.string()).describe('A list of follow-up queries to address the knowledge gap.'),
});

export type Reflection = z.infer<typeof ReflectionSchema>;

/**
 * Legacy class exports for backward compatibility
 */
export class SearchQueryListClass {
  query: string[];
  rationale: string;

  constructor(params: { query: string[], rationale: string }) {
    this.query = params.query;
    this.rationale = params.rationale;
  }
}

export class ReflectionClass {
  is_sufficient: boolean;
  knowledge_gap: string;
  follow_up_queries: string[];

  constructor(params: { is_sufficient: boolean, knowledge_gap: string, follow_up_queries: string[] }) {
    this.is_sufficient = params.is_sufficient;
    this.knowledge_gap = params.knowledge_gap;
    this.follow_up_queries = params.follow_up_queries;
  }
}
