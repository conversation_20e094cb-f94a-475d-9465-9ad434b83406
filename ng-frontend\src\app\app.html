<!-- Angular v20 Zoneless Research Assistant -->
<div class="app-container">
  @if (!hasMessages()) {
    <!-- Welcome Screen -->
    <app-welcome-screen
      [isLoading]="isLoading()"
      (submitQuery)="onSubmit($event.inputValue, $event.config)"
      (cancel)="onCancel()">
    </app-welcome-screen>
  } @else {
    <!-- Chat Messages View -->
    <app-chat-messages-view
      #scrollContainer
      [messages]="messages()"
      [isLoading]="isLoading()"
      [liveActivityEvents]="processedEventsTimeline()"
      [historicalActivities]="historicalActivities()"
      (submitQuery)="onSubmit($event.inputValue, $event.config)"
      (cancel)="onCancel()"
      (newSearch)="onNewSearch()">
    </app-chat-messages-view>
  }
</div>
