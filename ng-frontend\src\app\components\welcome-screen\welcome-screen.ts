import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InputForm } from '../input-form/input-form';
import { ResearchConfig } from '../../models/message.model';

@Component({
  selector: 'app-welcome-screen',
  imports: [CommonModule, InputForm],
  templateUrl: './welcome-screen.html',
  styleUrl: './welcome-screen.scss'
})
export class WelcomeScreen {
  // Signal inputs
  isLoading = input<boolean>(false);

  // Signal outputs
  submitQuery = output<{ inputValue: string; config: ResearchConfig }>();
  cancel = output<void>();

  onSubmit(event: { inputValue: string; config: ResearchConfig }): void {
    this.submitQuery.emit(event);
  }

  onCancel(): void {
    this.cancel.emit();
  }
}
