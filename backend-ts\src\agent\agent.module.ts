import { Modu<PERSON> } from '@nestjs/common';
import { AgentService } from './agent.service';
import { GraphService } from './graph.service';
import { ConfigurationService } from './configuration.service';
import { AgentController } from './agent.controller';
import { Lang<PERSON>raphController } from './langgraph.controller';

@Module({
  controllers: [AgentController, LangGraphController],
  providers: [AgentService, GraphService, ConfigurationService],
  exports: [AgentService, GraphService],
})
export class AgentModule {}
