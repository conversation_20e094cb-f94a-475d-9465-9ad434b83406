import { Component, input, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';
import { MarkdownModule } from 'ngx-markdown';
import { InputForm } from '../input-form/input-form';
import { ActivityTimeline } from '../activity-timeline/activity-timeline';
import { Message, ProcessedEvent, ResearchConfig } from '../../models/message.model';

@Component({
  selector: 'app-chat-messages-view',
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatBadgeModule,
    MarkdownModule,
    InputForm,
    ActivityTimeline
  ],
  templateUrl: './chat-messages-view.html',
  styleUrl: './chat-messages-view.scss'
})
export class ChatMessagesView {
  // Signal inputs
  messages = input<Message[]>([]);
  isLoading = input<boolean>(false);
  liveActivityEvents = input<ProcessedEvent[]>([]);
  historicalActivities = input<Record<string, ProcessedEvent[]>>({});

  // Signal outputs
  submitQuery = output<{ inputValue: string; config: ResearchConfig }>();
  cancel = output<void>();
  newSearch = output<void>();

  // Internal signals
  copyFeedback = signal<string>('');

  constructor(private snackBar: MatSnackBar) {}

  onSubmit(event: { inputValue: string; config: ResearchConfig }): void {
    this.submitQuery.emit(event);
  }

  onCancel(): void {
    this.cancel.emit();
  }

  onNewSearch(): void {
    this.newSearch.emit();
  }

  async copyToClipboard(content: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(content);
      this.snackBar.open('Copied to clipboard!', 'Close', {
        duration: 2000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });
    } catch (err) {
      console.error('Failed to copy text: ', err);
      this.snackBar.open('Failed to copy text', 'Close', {
        duration: 2000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });
    }
  }

  getActivityEventsForMessage(messageId: string): ProcessedEvent[] {
    return this.historicalActivities()[messageId] || [];
  }

  isLastMessage(index: number): boolean {
    return index === this.messages().length - 1;
  }
}
