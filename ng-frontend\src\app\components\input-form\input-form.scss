.input-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: rgba(64, 64, 64, 0.8);
  border-radius: 1rem;
  backdrop-filter: blur(10px);

  .input-container {
    display: flex;
    align-items: flex-end;
    gap: 1rem;

    .input-field {
      flex: 1;

      .query-textarea {
        min-height: 60px;
        max-height: 200px;
        resize: vertical;
        font-size: 1rem;
        line-height: 1.5;
      }
    }

    .submit-button-container {
      display: flex;
      align-items: center;
      margin-bottom: 1.25rem;

      .submit-button {
        min-width: 120px;
        height: 48px;
        font-weight: 500;

        mat-icon {
          margin-right: 0.5rem;
        }
      }

      .action-button {
        width: 48px;
        height: 48px;
      }
    }
  }

  .config-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;

    .config-controls {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;

      .config-field {
        min-width: 140px;

        mat-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }

        .model-option {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;

            // Model-specific icon colors to match React version
            &.icon-gemini_2_0_flash {
              color: #ffc107; // Yellow for Flash
            }

            &.icon-gemini_2_5_flash_preview_04_17 {
              color: #ff9800; // Orange for 2.5 Flash
            }

            &.icon-gemini_2_5_pro_preview_05_06 {
              color: #9c27b0; // Purple for Pro
            }
          }
        }
      }
    }

    .new-search-button {
      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .input-form {
    .input-container {
      flex-direction: column;
      align-items: stretch;

      .submit-button-container {
        margin-bottom: 0;
        justify-content: center;
      }
    }

    .config-container {
      flex-direction: column;
      align-items: stretch;

      .config-controls {
        justify-content: center;
      }
    }
  }
}

// Dark theme adjustments
:host {
  --mdc-outlined-text-field-label-text-color: #e0e0e0;
  --mdc-outlined-text-field-input-text-color: #ffffff;
  --mdc-outlined-text-field-outline-color: #666666;
  --mdc-outlined-text-field-hover-outline-color: #888888;
  --mdc-outlined-text-field-focused-outline-color: #2196f3;
}
