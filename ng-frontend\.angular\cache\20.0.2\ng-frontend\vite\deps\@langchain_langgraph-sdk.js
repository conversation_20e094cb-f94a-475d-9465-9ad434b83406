import {
  __commonJS,
  __spreadProps,
  __spreadValues,
  __toESM
} from "./chunk-46DXP6YY.js";

// node_modules/.pnpm/retry@0.13.1/node_modules/retry/lib/retry_operation.js
var require_retry_operation = __commonJS({
  "node_modules/.pnpm/retry@0.13.1/node_modules/retry/lib/retry_operation.js"(exports, module) {
    function RetryOperation(timeouts, options) {
      if (typeof options === "boolean") {
        options = { forever: options };
      }
      this._originalTimeouts = JSON.parse(JSON.stringify(timeouts));
      this._timeouts = timeouts;
      this._options = options || {};
      this._maxRetryTime = options && options.maxRetryTime || Infinity;
      this._fn = null;
      this._errors = [];
      this._attempts = 1;
      this._operationTimeout = null;
      this._operationTimeoutCb = null;
      this._timeout = null;
      this._operationStart = null;
      this._timer = null;
      if (this._options.forever) {
        this._cachedTimeouts = this._timeouts.slice(0);
      }
    }
    module.exports = RetryOperation;
    RetryOperation.prototype.reset = function() {
      this._attempts = 1;
      this._timeouts = this._originalTimeouts.slice(0);
    };
    RetryOperation.prototype.stop = function() {
      if (this._timeout) {
        clearTimeout(this._timeout);
      }
      if (this._timer) {
        clearTimeout(this._timer);
      }
      this._timeouts = [];
      this._cachedTimeouts = null;
    };
    RetryOperation.prototype.retry = function(err) {
      if (this._timeout) {
        clearTimeout(this._timeout);
      }
      if (!err) {
        return false;
      }
      var currentTime = (/* @__PURE__ */ new Date()).getTime();
      if (err && currentTime - this._operationStart >= this._maxRetryTime) {
        this._errors.push(err);
        this._errors.unshift(new Error("RetryOperation timeout occurred"));
        return false;
      }
      this._errors.push(err);
      var timeout = this._timeouts.shift();
      if (timeout === void 0) {
        if (this._cachedTimeouts) {
          this._errors.splice(0, this._errors.length - 1);
          timeout = this._cachedTimeouts.slice(-1);
        } else {
          return false;
        }
      }
      var self = this;
      this._timer = setTimeout(function() {
        self._attempts++;
        if (self._operationTimeoutCb) {
          self._timeout = setTimeout(function() {
            self._operationTimeoutCb(self._attempts);
          }, self._operationTimeout);
          if (self._options.unref) {
            self._timeout.unref();
          }
        }
        self._fn(self._attempts);
      }, timeout);
      if (this._options.unref) {
        this._timer.unref();
      }
      return true;
    };
    RetryOperation.prototype.attempt = function(fn, timeoutOps) {
      this._fn = fn;
      if (timeoutOps) {
        if (timeoutOps.timeout) {
          this._operationTimeout = timeoutOps.timeout;
        }
        if (timeoutOps.cb) {
          this._operationTimeoutCb = timeoutOps.cb;
        }
      }
      var self = this;
      if (this._operationTimeoutCb) {
        this._timeout = setTimeout(function() {
          self._operationTimeoutCb();
        }, self._operationTimeout);
      }
      this._operationStart = (/* @__PURE__ */ new Date()).getTime();
      this._fn(this._attempts);
    };
    RetryOperation.prototype.try = function(fn) {
      console.log("Using RetryOperation.try() is deprecated");
      this.attempt(fn);
    };
    RetryOperation.prototype.start = function(fn) {
      console.log("Using RetryOperation.start() is deprecated");
      this.attempt(fn);
    };
    RetryOperation.prototype.start = RetryOperation.prototype.try;
    RetryOperation.prototype.errors = function() {
      return this._errors;
    };
    RetryOperation.prototype.attempts = function() {
      return this._attempts;
    };
    RetryOperation.prototype.mainError = function() {
      if (this._errors.length === 0) {
        return null;
      }
      var counts = {};
      var mainError = null;
      var mainErrorCount = 0;
      for (var i = 0; i < this._errors.length; i++) {
        var error = this._errors[i];
        var message = error.message;
        var count = (counts[message] || 0) + 1;
        counts[message] = count;
        if (count >= mainErrorCount) {
          mainError = error;
          mainErrorCount = count;
        }
      }
      return mainError;
    };
  }
});

// node_modules/.pnpm/retry@0.13.1/node_modules/retry/lib/retry.js
var require_retry = __commonJS({
  "node_modules/.pnpm/retry@0.13.1/node_modules/retry/lib/retry.js"(exports) {
    var RetryOperation = require_retry_operation();
    exports.operation = function(options) {
      var timeouts = exports.timeouts(options);
      return new RetryOperation(timeouts, {
        forever: options && (options.forever || options.retries === Infinity),
        unref: options && options.unref,
        maxRetryTime: options && options.maxRetryTime
      });
    };
    exports.timeouts = function(options) {
      if (options instanceof Array) {
        return [].concat(options);
      }
      var opts = {
        retries: 10,
        factor: 2,
        minTimeout: 1 * 1e3,
        maxTimeout: Infinity,
        randomize: false
      };
      for (var key in options) {
        opts[key] = options[key];
      }
      if (opts.minTimeout > opts.maxTimeout) {
        throw new Error("minTimeout is greater than maxTimeout");
      }
      var timeouts = [];
      for (var i = 0; i < opts.retries; i++) {
        timeouts.push(this.createTimeout(i, opts));
      }
      if (options && options.forever && !timeouts.length) {
        timeouts.push(this.createTimeout(i, opts));
      }
      timeouts.sort(function(a, b) {
        return a - b;
      });
      return timeouts;
    };
    exports.createTimeout = function(attempt, opts) {
      var random = opts.randomize ? Math.random() + 1 : 1;
      var timeout = Math.round(random * Math.max(opts.minTimeout, 1) * Math.pow(opts.factor, attempt));
      timeout = Math.min(timeout, opts.maxTimeout);
      return timeout;
    };
    exports.wrap = function(obj, options, methods) {
      if (options instanceof Array) {
        methods = options;
        options = null;
      }
      if (!methods) {
        methods = [];
        for (var key in obj) {
          if (typeof obj[key] === "function") {
            methods.push(key);
          }
        }
      }
      for (var i = 0; i < methods.length; i++) {
        var method = methods[i];
        var original = obj[method];
        obj[method] = (function retryWrapper(original2) {
          var op = exports.operation(options);
          var args = Array.prototype.slice.call(arguments, 1);
          var callback = args.pop();
          args.push(function(err) {
            if (op.retry(err)) {
              return;
            }
            if (err) {
              arguments[0] = op.mainError();
            }
            callback.apply(this, arguments);
          });
          op.attempt(function() {
            original2.apply(obj, args);
          });
        }).bind(obj, original);
        obj[method].options = options;
      }
    };
  }
});

// node_modules/.pnpm/retry@0.13.1/node_modules/retry/index.js
var require_retry2 = __commonJS({
  "node_modules/.pnpm/retry@0.13.1/node_modules/retry/index.js"(exports, module) {
    module.exports = require_retry();
  }
});

// node_modules/.pnpm/p-retry@4.6.2/node_modules/p-retry/index.js
var require_p_retry = __commonJS({
  "node_modules/.pnpm/p-retry@4.6.2/node_modules/p-retry/index.js"(exports, module) {
    "use strict";
    var retry = require_retry2();
    var networkErrorMsgs = [
      "Failed to fetch",
      // Chrome
      "NetworkError when attempting to fetch resource.",
      // Firefox
      "The Internet connection appears to be offline.",
      // Safari
      "Network request failed"
      // `cross-fetch`
    ];
    var AbortError = class extends Error {
      constructor(message) {
        super();
        if (message instanceof Error) {
          this.originalError = message;
          ({ message } = message);
        } else {
          this.originalError = new Error(message);
          this.originalError.stack = this.stack;
        }
        this.name = "AbortError";
        this.message = message;
      }
    };
    var decorateErrorWithCounts = (error, attemptNumber, options) => {
      const retriesLeft = options.retries - (attemptNumber - 1);
      error.attemptNumber = attemptNumber;
      error.retriesLeft = retriesLeft;
      return error;
    };
    var isNetworkError = (errorMessage) => networkErrorMsgs.includes(errorMessage);
    var pRetry2 = (input, options) => new Promise((resolve, reject) => {
      options = __spreadValues({
        onFailedAttempt: () => {
        },
        retries: 10
      }, options);
      const operation = retry.operation(options);
      operation.attempt(async (attemptNumber) => {
        try {
          resolve(await input(attemptNumber));
        } catch (error) {
          if (!(error instanceof Error)) {
            reject(new TypeError(`Non-error was thrown: "${error}". You should only throw errors.`));
            return;
          }
          if (error instanceof AbortError) {
            operation.stop();
            reject(error.originalError);
          } else if (error instanceof TypeError && !isNetworkError(error.message)) {
            operation.stop();
            reject(error);
          } else {
            decorateErrorWithCounts(error, attemptNumber, options);
            try {
              await options.onFailedAttempt(error);
            } catch (error2) {
              reject(error2);
              return;
            }
            if (!operation.retry(error)) {
              reject(operation.mainError());
            }
          }
        }
      });
    });
    module.exports = pRetry2;
    module.exports.default = pRetry2;
    module.exports.AbortError = AbortError;
  }
});

// node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js"(exports, module) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__) prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
      else emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0) emitter._events = new Events();
      else delete emitter._events[evt];
    }
    function EventEmitter() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0) return names;
      for (name in events = this._events) {
        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers) return [];
      if (handlers.fn) return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners) return 0;
      if (listeners.fn) return 1;
      return listeners.length;
    };
    EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once) this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once) this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {
                args[j - 1] = arguments[j];
              }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
        else clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt]) clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.addListener = EventEmitter.prototype.on;
    EventEmitter.prefixed = prefix;
    EventEmitter.EventEmitter = EventEmitter;
    if ("undefined" !== typeof module) {
      module.exports = EventEmitter;
    }
  }
});

// node_modules/.pnpm/p-finally@1.0.0/node_modules/p-finally/index.js
var require_p_finally = __commonJS({
  "node_modules/.pnpm/p-finally@1.0.0/node_modules/p-finally/index.js"(exports, module) {
    "use strict";
    module.exports = (promise, onFinally) => {
      onFinally = onFinally || (() => {
      });
      return promise.then(
        (val) => new Promise((resolve) => {
          resolve(onFinally());
        }).then(() => val),
        (err) => new Promise((resolve) => {
          resolve(onFinally());
        }).then(() => {
          throw err;
        })
      );
    };
  }
});

// node_modules/.pnpm/p-timeout@3.2.0/node_modules/p-timeout/index.js
var require_p_timeout = __commonJS({
  "node_modules/.pnpm/p-timeout@3.2.0/node_modules/p-timeout/index.js"(exports, module) {
    "use strict";
    var pFinally = require_p_finally();
    var TimeoutError = class extends Error {
      constructor(message) {
        super(message);
        this.name = "TimeoutError";
      }
    };
    var pTimeout = (promise, milliseconds, fallback) => new Promise((resolve, reject) => {
      if (typeof milliseconds !== "number" || milliseconds < 0) {
        throw new TypeError("Expected `milliseconds` to be a positive number");
      }
      if (milliseconds === Infinity) {
        resolve(promise);
        return;
      }
      const timer = setTimeout(() => {
        if (typeof fallback === "function") {
          try {
            resolve(fallback());
          } catch (error) {
            reject(error);
          }
          return;
        }
        const message = typeof fallback === "string" ? fallback : `Promise timed out after ${milliseconds} milliseconds`;
        const timeoutError = fallback instanceof Error ? fallback : new TimeoutError(message);
        if (typeof promise.cancel === "function") {
          promise.cancel();
        }
        reject(timeoutError);
      }, milliseconds);
      pFinally(
        // eslint-disable-next-line promise/prefer-await-to-then
        promise.then(resolve, reject),
        () => {
          clearTimeout(timer);
        }
      );
    });
    module.exports = pTimeout;
    module.exports.default = pTimeout;
    module.exports.TimeoutError = TimeoutError;
  }
});

// node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/lower-bound.js
var require_lower_bound = __commonJS({
  "node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/lower-bound.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function lowerBound(array, value, comparator) {
      let first = 0;
      let count = array.length;
      while (count > 0) {
        const step = count / 2 | 0;
        let it = first + step;
        if (comparator(array[it], value) <= 0) {
          first = ++it;
          count -= step + 1;
        } else {
          count = step;
        }
      }
      return first;
    }
    exports.default = lowerBound;
  }
});

// node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/priority-queue.js
var require_priority_queue = __commonJS({
  "node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/priority-queue.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var lower_bound_1 = require_lower_bound();
    var PriorityQueue = class {
      constructor() {
        this._queue = [];
      }
      enqueue(run, options) {
        options = Object.assign({ priority: 0 }, options);
        const element = {
          priority: options.priority,
          run
        };
        if (this.size && this._queue[this.size - 1].priority >= options.priority) {
          this._queue.push(element);
          return;
        }
        const index = lower_bound_1.default(this._queue, element, (a, b) => b.priority - a.priority);
        this._queue.splice(index, 0, element);
      }
      dequeue() {
        const item = this._queue.shift();
        return item === null || item === void 0 ? void 0 : item.run;
      }
      filter(options) {
        return this._queue.filter((element) => element.priority === options.priority).map((element) => element.run);
      }
      get size() {
        return this._queue.length;
      }
    };
    exports.default = PriorityQueue;
  }
});

// node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var EventEmitter = require_eventemitter3();
    var p_timeout_1 = require_p_timeout();
    var priority_queue_1 = require_priority_queue();
    var empty = () => {
    };
    var timeoutError = new p_timeout_1.TimeoutError();
    var PQueue = class extends EventEmitter {
      constructor(options) {
        var _a, _b, _c, _d;
        super();
        this._intervalCount = 0;
        this._intervalEnd = 0;
        this._pendingCount = 0;
        this._resolveEmpty = empty;
        this._resolveIdle = empty;
        options = Object.assign({ carryoverConcurrencyCount: false, intervalCap: Infinity, interval: 0, concurrency: Infinity, autoStart: true, queueClass: priority_queue_1.default }, options);
        if (!(typeof options.intervalCap === "number" && options.intervalCap >= 1)) {
          throw new TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${(_b = (_a = options.intervalCap) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : ""}\` (${typeof options.intervalCap})`);
        }
        if (options.interval === void 0 || !(Number.isFinite(options.interval) && options.interval >= 0)) {
          throw new TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${(_d = (_c = options.interval) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : ""}\` (${typeof options.interval})`);
        }
        this._carryoverConcurrencyCount = options.carryoverConcurrencyCount;
        this._isIntervalIgnored = options.intervalCap === Infinity || options.interval === 0;
        this._intervalCap = options.intervalCap;
        this._interval = options.interval;
        this._queue = new options.queueClass();
        this._queueClass = options.queueClass;
        this.concurrency = options.concurrency;
        this._timeout = options.timeout;
        this._throwOnTimeout = options.throwOnTimeout === true;
        this._isPaused = options.autoStart === false;
      }
      get _doesIntervalAllowAnother() {
        return this._isIntervalIgnored || this._intervalCount < this._intervalCap;
      }
      get _doesConcurrentAllowAnother() {
        return this._pendingCount < this._concurrency;
      }
      _next() {
        this._pendingCount--;
        this._tryToStartAnother();
        this.emit("next");
      }
      _resolvePromises() {
        this._resolveEmpty();
        this._resolveEmpty = empty;
        if (this._pendingCount === 0) {
          this._resolveIdle();
          this._resolveIdle = empty;
          this.emit("idle");
        }
      }
      _onResumeInterval() {
        this._onInterval();
        this._initializeIntervalIfNeeded();
        this._timeoutId = void 0;
      }
      _isIntervalPaused() {
        const now = Date.now();
        if (this._intervalId === void 0) {
          const delay = this._intervalEnd - now;
          if (delay < 0) {
            this._intervalCount = this._carryoverConcurrencyCount ? this._pendingCount : 0;
          } else {
            if (this._timeoutId === void 0) {
              this._timeoutId = setTimeout(() => {
                this._onResumeInterval();
              }, delay);
            }
            return true;
          }
        }
        return false;
      }
      _tryToStartAnother() {
        if (this._queue.size === 0) {
          if (this._intervalId) {
            clearInterval(this._intervalId);
          }
          this._intervalId = void 0;
          this._resolvePromises();
          return false;
        }
        if (!this._isPaused) {
          const canInitializeInterval = !this._isIntervalPaused();
          if (this._doesIntervalAllowAnother && this._doesConcurrentAllowAnother) {
            const job = this._queue.dequeue();
            if (!job) {
              return false;
            }
            this.emit("active");
            job();
            if (canInitializeInterval) {
              this._initializeIntervalIfNeeded();
            }
            return true;
          }
        }
        return false;
      }
      _initializeIntervalIfNeeded() {
        if (this._isIntervalIgnored || this._intervalId !== void 0) {
          return;
        }
        this._intervalId = setInterval(() => {
          this._onInterval();
        }, this._interval);
        this._intervalEnd = Date.now() + this._interval;
      }
      _onInterval() {
        if (this._intervalCount === 0 && this._pendingCount === 0 && this._intervalId) {
          clearInterval(this._intervalId);
          this._intervalId = void 0;
        }
        this._intervalCount = this._carryoverConcurrencyCount ? this._pendingCount : 0;
        this._processQueue();
      }
      /**
      Executes all queued functions until it reaches the limit.
      */
      _processQueue() {
        while (this._tryToStartAnother()) {
        }
      }
      get concurrency() {
        return this._concurrency;
      }
      set concurrency(newConcurrency) {
        if (!(typeof newConcurrency === "number" && newConcurrency >= 1)) {
          throw new TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${newConcurrency}\` (${typeof newConcurrency})`);
        }
        this._concurrency = newConcurrency;
        this._processQueue();
      }
      /**
      Adds a sync or async task to the queue. Always returns a promise.
      */
      async add(fn, options = {}) {
        return new Promise((resolve, reject) => {
          const run = async () => {
            this._pendingCount++;
            this._intervalCount++;
            try {
              const operation = this._timeout === void 0 && options.timeout === void 0 ? fn() : p_timeout_1.default(Promise.resolve(fn()), options.timeout === void 0 ? this._timeout : options.timeout, () => {
                if (options.throwOnTimeout === void 0 ? this._throwOnTimeout : options.throwOnTimeout) {
                  reject(timeoutError);
                }
                return void 0;
              });
              resolve(await operation);
            } catch (error) {
              reject(error);
            }
            this._next();
          };
          this._queue.enqueue(run, options);
          this._tryToStartAnother();
          this.emit("add");
        });
      }
      /**
          Same as `.add()`, but accepts an array of sync or async functions.
      
          @returns A promise that resolves when all functions are resolved.
          */
      async addAll(functions, options) {
        return Promise.all(functions.map(async (function_) => this.add(function_, options)));
      }
      /**
      Start (or resume) executing enqueued tasks within concurrency limit. No need to call this if queue is not paused (via `options.autoStart = false` or by `.pause()` method.)
      */
      start() {
        if (!this._isPaused) {
          return this;
        }
        this._isPaused = false;
        this._processQueue();
        return this;
      }
      /**
      Put queue execution on hold.
      */
      pause() {
        this._isPaused = true;
      }
      /**
      Clear the queue.
      */
      clear() {
        this._queue = new this._queueClass();
      }
      /**
          Can be called multiple times. Useful if you for example add additional items at a later time.
      
          @returns A promise that settles when the queue becomes empty.
          */
      async onEmpty() {
        if (this._queue.size === 0) {
          return;
        }
        return new Promise((resolve) => {
          const existingResolve = this._resolveEmpty;
          this._resolveEmpty = () => {
            existingResolve();
            resolve();
          };
        });
      }
      /**
          The difference with `.onEmpty` is that `.onIdle` guarantees that all work from the queue has finished. `.onEmpty` merely signals that the queue is empty, but it could mean that some promises haven't completed yet.
      
          @returns A promise that settles when the queue becomes empty, and all promises have completed; `queue.size === 0 && queue.pending === 0`.
          */
      async onIdle() {
        if (this._pendingCount === 0 && this._queue.size === 0) {
          return;
        }
        return new Promise((resolve) => {
          const existingResolve = this._resolveIdle;
          this._resolveIdle = () => {
            existingResolve();
            resolve();
          };
        });
      }
      /**
      Size of the queue.
      */
      get size() {
        return this._queue.size;
      }
      /**
          Size of the queue, filtered by the given options.
      
          For example, this can be used to find the number of items remaining in the queue with a specific priority level.
          */
      sizeBy(options) {
        return this._queue.filter(options).length;
      }
      /**
      Number of pending promises.
      */
      get pending() {
        return this._pendingCount;
      }
      /**
      Whether the queue is currently paused.
      */
      get isPaused() {
        return this._isPaused;
      }
      get timeout() {
        return this._timeout;
      }
      /**
      Set the timeout for future operations.
      */
      set timeout(milliseconds) {
        this._timeout = milliseconds;
      }
    };
    exports.default = PQueue;
  }
});

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js
var import_p_retry = __toESM(require_p_retry(), 1);
var import_p_queue = __toESM(require_dist(), 1);

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js
var DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);
var LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for("lg:fetch_implementation");
var overrideFetchImplementation = (fetch2) => {
  globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch2;
};
var _getFetchImplementation = () => {
  return globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ?? DEFAULT_FETCH_IMPLEMENTATION;
};

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js
var STATUS_NO_RETRY = [
  400,
  // Bad Request
  401,
  // Unauthorized
  402,
  // Payment required
  403,
  // Forbidden
  404,
  // Not Found
  405,
  // Method Not Allowed
  406,
  // Not Acceptable
  407,
  // Proxy Authentication Required
  408,
  // Request Timeout
  409,
  // Conflict
  422
  // Unprocessable Entity
];
function isResponse(x) {
  if (x == null || typeof x !== "object")
    return false;
  return "status" in x && "statusText" in x && "text" in x;
}
var HTTPError = class _HTTPError extends Error {
  constructor(status, message, response) {
    super(`HTTP ${status}: ${message}`);
    Object.defineProperty(this, "status", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "text", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "response", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    this.status = status;
    this.text = message;
    this.response = response;
  }
  static async fromResponse(response, options) {
    try {
      return new _HTTPError(response.status, await response.text(), options?.includeResponse ? response : void 0);
    } catch {
      return new _HTTPError(response.status, response.statusText, options?.includeResponse ? response : void 0);
    }
  }
};
var AsyncCaller = class {
  constructor(params) {
    Object.defineProperty(this, "maxConcurrency", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "maxRetries", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "queue", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "onFailedResponseHook", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "customFetch", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    this.maxConcurrency = params.maxConcurrency ?? Infinity;
    this.maxRetries = params.maxRetries ?? 4;
    if ("default" in import_p_queue.default) {
      this.queue = new import_p_queue.default.default({
        concurrency: this.maxConcurrency
      });
    } else {
      this.queue = new import_p_queue.default({ concurrency: this.maxConcurrency });
    }
    this.onFailedResponseHook = params?.onFailedResponseHook;
    this.customFetch = params.fetch;
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  call(callable, ...args) {
    const onFailedResponseHook = this.onFailedResponseHook;
    return this.queue.add(() => (0, import_p_retry.default)(() => callable(...args).catch(async (error) => {
      if (error instanceof Error) {
        throw error;
      } else if (isResponse(error)) {
        throw await HTTPError.fromResponse(error, {
          includeResponse: !!onFailedResponseHook
        });
      } else {
        throw new Error(error);
      }
    }), {
      async onFailedAttempt(error) {
        if (error.message.startsWith("Cancel") || error.message.startsWith("TimeoutError") || error.message.startsWith("AbortError")) {
          throw error;
        }
        if (error?.code === "ECONNABORTED") {
          throw error;
        }
        if (error instanceof HTTPError) {
          if (STATUS_NO_RETRY.includes(error.status)) {
            throw error;
          }
          if (onFailedResponseHook && error.response) {
            await onFailedResponseHook(error.response);
          }
        }
      },
      // If needed we can change some of the defaults here,
      // but they're quite sensible.
      retries: this.maxRetries,
      randomize: true
    }), { throwOnTimeout: true });
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  callWithOptions(options, callable, ...args) {
    if (options.signal) {
      return Promise.race([
        this.call(callable, ...args),
        new Promise((_, reject) => {
          options.signal?.addEventListener("abort", () => {
            reject(new Error("AbortError"));
          });
        })
      ]);
    }
    return this.call(callable, ...args);
  }
  fetch(...args) {
    const fetchFn = this.customFetch ?? _getFetchImplementation();
    return this.call(() => fetchFn(...args).then((res) => res.ok ? res : Promise.reject(res)));
  }
};

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/env.js
function getEnvironmentVariable(name) {
  try {
    return typeof process !== "undefined" ? (
      // eslint-disable-next-line no-process-env
      process.env?.[name]
    ) : void 0;
  } catch (e) {
    return void 0;
  }
}

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js
function mergeSignals(...signals) {
  const nonZeroSignals = signals.filter((signal) => signal != null);
  if (nonZeroSignals.length === 0)
    return void 0;
  if (nonZeroSignals.length === 1)
    return nonZeroSignals[0];
  const controller = new AbortController();
  for (const signal of signals) {
    if (signal?.aborted) {
      controller.abort(signal.reason);
      return controller.signal;
    }
    signal?.addEventListener("abort", () => controller.abort(signal.reason), {
      once: true
    });
  }
  return controller.signal;
}

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js
var CR = "\r".charCodeAt(0);
var LF = "\n".charCodeAt(0);
var NULL = "\0".charCodeAt(0);
var COLON = ":".charCodeAt(0);
var SPACE = " ".charCodeAt(0);
var TRAILING_NEWLINE = [CR, LF];
var BytesLineDecoder = class extends TransformStream {
  constructor() {
    let buffer = [];
    let trailingCr = false;
    super({
      start() {
        buffer = [];
        trailingCr = false;
      },
      transform(chunk, controller) {
        let text = chunk;
        if (trailingCr) {
          text = joinArrays([[CR], text]);
          trailingCr = false;
        }
        if (text.length > 0 && text.at(-1) === CR) {
          trailingCr = true;
          text = text.subarray(0, -1);
        }
        if (!text.length)
          return;
        const trailingNewline = TRAILING_NEWLINE.includes(text.at(-1));
        const lastIdx = text.length - 1;
        const { lines } = text.reduce((acc, cur, idx) => {
          if (acc.from > idx)
            return acc;
          if (cur === CR || cur === LF) {
            acc.lines.push(text.subarray(acc.from, idx));
            if (cur === CR && text[idx + 1] === LF) {
              acc.from = idx + 2;
            } else {
              acc.from = idx + 1;
            }
          }
          if (idx === lastIdx && acc.from <= lastIdx) {
            acc.lines.push(text.subarray(acc.from));
          }
          return acc;
        }, { lines: [], from: 0 });
        if (lines.length === 1 && !trailingNewline) {
          buffer.push(lines[0]);
          return;
        }
        if (buffer.length) {
          buffer.push(lines[0]);
          lines[0] = joinArrays(buffer);
          buffer = [];
        }
        if (!trailingNewline) {
          if (lines.length)
            buffer = [lines.pop()];
        }
        for (const line of lines) {
          controller.enqueue(line);
        }
      },
      flush(controller) {
        if (buffer.length) {
          controller.enqueue(joinArrays(buffer));
        }
      }
    });
  }
};
var SSEDecoder = class extends TransformStream {
  constructor() {
    let event = "";
    let data = [];
    let lastEventId = "";
    let retry = null;
    const decoder = new TextDecoder();
    super({
      transform(chunk, controller) {
        if (!chunk.length) {
          if (!event && !data.length && !lastEventId && retry == null)
            return;
          const sse = {
            id: lastEventId || void 0,
            event,
            data: data.length ? decodeArraysToJson(decoder, data) : null
          };
          event = "";
          data = [];
          retry = null;
          controller.enqueue(sse);
          return;
        }
        if (chunk[0] === COLON)
          return;
        const sepIdx = chunk.indexOf(COLON);
        if (sepIdx === -1)
          return;
        const fieldName = decoder.decode(chunk.subarray(0, sepIdx));
        let value = chunk.subarray(sepIdx + 1);
        if (value[0] === SPACE)
          value = value.subarray(1);
        if (fieldName === "event") {
          event = decoder.decode(value);
        } else if (fieldName === "data") {
          data.push(value);
        } else if (fieldName === "id") {
          if (value.indexOf(NULL) === -1)
            lastEventId = decoder.decode(value);
        } else if (fieldName === "retry") {
          const retryNum = Number.parseInt(decoder.decode(value));
          if (!Number.isNaN(retryNum))
            retry = retryNum;
        }
      },
      flush(controller) {
        if (event) {
          controller.enqueue({
            id: lastEventId || void 0,
            event,
            data: data.length ? decodeArraysToJson(decoder, data) : null
          });
        }
      }
    });
  }
};
function joinArrays(data) {
  const totalLength = data.reduce((acc, curr) => acc + curr.length, 0);
  let merged = new Uint8Array(totalLength);
  let offset = 0;
  for (const c of data) {
    merged.set(c, offset);
    offset += c.length;
  }
  return merged;
}
function decodeArraysToJson(decoder, data) {
  return JSON.parse(decoder.decode(joinArrays(data)));
}

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js
var IterableReadableStream = class _IterableReadableStream extends ReadableStream {
  constructor() {
    super(...arguments);
    Object.defineProperty(this, "reader", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
  }
  ensureReader() {
    if (!this.reader) {
      this.reader = this.getReader();
    }
  }
  async next() {
    this.ensureReader();
    try {
      const result = await this.reader.read();
      if (result.done) {
        this.reader.releaseLock();
        return {
          done: true,
          value: void 0
        };
      } else {
        return {
          done: false,
          value: result.value
        };
      }
    } catch (e) {
      this.reader.releaseLock();
      throw e;
    }
  }
  async return() {
    this.ensureReader();
    if (this.locked) {
      const cancelPromise = this.reader.cancel();
      this.reader.releaseLock();
      await cancelPromise;
    }
    return { done: true, value: void 0 };
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async throw(e) {
    this.ensureReader();
    if (this.locked) {
      const cancelPromise = this.reader.cancel();
      this.reader.releaseLock();
      await cancelPromise;
    }
    throw e;
  }
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore Not present in Node 18 types, required in latest Node 22
  async [Symbol.asyncDispose]() {
    await this.return();
  }
  [Symbol.asyncIterator]() {
    return this;
  }
  static fromReadableStream(stream) {
    const reader = stream.getReader();
    return new _IterableReadableStream({
      start(controller) {
        return pump();
        function pump() {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }
            controller.enqueue(value);
            return pump();
          });
        }
      },
      cancel() {
        reader.releaseLock();
      }
    });
  }
  static fromAsyncGenerator(generator) {
    return new _IterableReadableStream({
      async pull(controller) {
        const { value, done } = await generator.next();
        if (done) {
          controller.close();
        }
        controller.enqueue(value);
      },
      async cancel(reason) {
        await generator.return(reason);
      }
    });
  }
};

// node_modules/.pnpm/@langchain+langgraph-sdk@0.0.84_@langchain+core@0.3.58/node_modules/@langchain/langgraph-sdk/dist/client.js
function getApiKey(apiKey) {
  if (apiKey) {
    return apiKey;
  }
  const prefixes = ["LANGGRAPH", "LANGSMITH", "LANGCHAIN"];
  for (const prefix of prefixes) {
    const envKey = getEnvironmentVariable(`${prefix}_API_KEY`);
    if (envKey) {
      return envKey.trim().replace(/^["']|["']$/g, "");
    }
  }
  return void 0;
}
var REGEX_RUN_METADATA = /(\/threads\/(?<thread_id>.+))?\/runs\/(?<run_id>.+)/;
function getRunMetadataFromResponse(response) {
  const contentLocation = response.headers.get("Content-Location");
  if (!contentLocation)
    return void 0;
  const match = REGEX_RUN_METADATA.exec(contentLocation);
  if (!match?.groups?.run_id)
    return void 0;
  return {
    run_id: match.groups.run_id,
    thread_id: match.groups.thread_id || void 0
  };
}
var BaseClient = class {
  constructor(config) {
    Object.defineProperty(this, "asyncCaller", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "timeoutMs", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "apiUrl", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "defaultHeaders", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "onRequest", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    const callerOptions = __spreadValues({
      maxRetries: 4,
      maxConcurrency: 4
    }, config?.callerOptions);
    let defaultApiUrl = "http://localhost:8123";
    if (!config?.apiUrl && typeof globalThis === "object" && globalThis != null) {
      const fetchSmb = Symbol.for("langgraph_api:fetch");
      const urlSmb = Symbol.for("langgraph_api:url");
      const global = globalThis;
      if (global[fetchSmb])
        callerOptions.fetch ??= global[fetchSmb];
      if (global[urlSmb])
        defaultApiUrl = global[urlSmb];
    }
    this.asyncCaller = new AsyncCaller(callerOptions);
    this.timeoutMs = config?.timeoutMs;
    this.apiUrl = config?.apiUrl?.replace(/\/$/, "") || defaultApiUrl;
    this.defaultHeaders = config?.defaultHeaders || {};
    this.onRequest = config?.onRequest;
    const apiKey = getApiKey(config?.apiKey);
    if (apiKey) {
      this.defaultHeaders["X-Api-Key"] = apiKey;
    }
  }
  prepareFetchOptions(path, options) {
    const mutatedOptions = __spreadProps(__spreadValues({}, options), {
      headers: __spreadValues(__spreadValues({}, this.defaultHeaders), options?.headers)
    });
    if (mutatedOptions.json) {
      mutatedOptions.body = JSON.stringify(mutatedOptions.json);
      mutatedOptions.headers = __spreadProps(__spreadValues({}, mutatedOptions.headers), {
        "Content-Type": "application/json"
      });
      delete mutatedOptions.json;
    }
    if (mutatedOptions.withResponse) {
      delete mutatedOptions.withResponse;
    }
    let timeoutSignal = null;
    if (typeof options?.timeoutMs !== "undefined") {
      if (options.timeoutMs != null) {
        timeoutSignal = AbortSignal.timeout(options.timeoutMs);
      }
    } else if (this.timeoutMs != null) {
      timeoutSignal = AbortSignal.timeout(this.timeoutMs);
    }
    mutatedOptions.signal = mergeSignals(timeoutSignal, mutatedOptions.signal);
    const targetUrl = new URL(`${this.apiUrl}${path}`);
    if (mutatedOptions.params) {
      for (const [key, value] of Object.entries(mutatedOptions.params)) {
        if (value == null)
          continue;
        let strValue = typeof value === "string" || typeof value === "number" ? value.toString() : JSON.stringify(value);
        targetUrl.searchParams.append(key, strValue);
      }
      delete mutatedOptions.params;
    }
    return [targetUrl, mutatedOptions];
  }
  async fetch(path, options) {
    const [url, init] = this.prepareFetchOptions(path, options);
    let finalInit = init;
    if (this.onRequest) {
      finalInit = await this.onRequest(url, init);
    }
    const response = await this.asyncCaller.fetch(url, finalInit);
    const body = (() => {
      if (response.status === 202 || response.status === 204) {
        return void 0;
      }
      return response.json();
    })();
    if (options?.withResponse) {
      return [await body, response];
    }
    return body;
  }
};
var CronsClient = class extends BaseClient {
  /**
   *
   * @param threadId The ID of the thread.
   * @param assistantId Assistant ID to use for this cron job.
   * @param payload Payload for creating a cron job.
   * @returns The created background run.
   */
  async createForThread(threadId, assistantId, payload) {
    const json = {
      schedule: payload?.schedule,
      input: payload?.input,
      config: payload?.config,
      metadata: payload?.metadata,
      assistant_id: assistantId,
      interrupt_before: payload?.interruptBefore,
      interrupt_after: payload?.interruptAfter,
      webhook: payload?.webhook,
      multitask_strategy: payload?.multitaskStrategy,
      if_not_exists: payload?.ifNotExists,
      checkpoint_during: payload?.checkpointDuring
    };
    return this.fetch(`/threads/${threadId}/runs/crons`, {
      method: "POST",
      json
    });
  }
  /**
   *
   * @param assistantId Assistant ID to use for this cron job.
   * @param payload Payload for creating a cron job.
   * @returns
   */
  async create(assistantId, payload) {
    const json = {
      schedule: payload?.schedule,
      input: payload?.input,
      config: payload?.config,
      metadata: payload?.metadata,
      assistant_id: assistantId,
      interrupt_before: payload?.interruptBefore,
      interrupt_after: payload?.interruptAfter,
      webhook: payload?.webhook,
      multitask_strategy: payload?.multitaskStrategy,
      if_not_exists: payload?.ifNotExists,
      checkpoint_during: payload?.checkpointDuring
    };
    return this.fetch(`/runs/crons`, {
      method: "POST",
      json
    });
  }
  /**
   *
   * @param cronId Cron ID of Cron job to delete.
   */
  async delete(cronId) {
    await this.fetch(`/runs/crons/${cronId}`, {
      method: "DELETE"
    });
  }
  /**
   *
   * @param query Query options.
   * @returns List of crons.
   */
  async search(query) {
    return this.fetch("/runs/crons/search", {
      method: "POST",
      json: {
        assistant_id: query?.assistantId ?? void 0,
        thread_id: query?.threadId ?? void 0,
        limit: query?.limit ?? 10,
        offset: query?.offset ?? 0
      }
    });
  }
};
var AssistantsClient = class extends BaseClient {
  /**
   * Get an assistant by ID.
   *
   * @param assistantId The ID of the assistant.
   * @returns Assistant
   */
  async get(assistantId) {
    return this.fetch(`/assistants/${assistantId}`);
  }
  /**
   * Get the JSON representation of the graph assigned to a runnable
   * @param assistantId The ID of the assistant.
   * @param options.xray Whether to include subgraphs in the serialized graph representation. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included.
   * @returns Serialized graph
   */
  async getGraph(assistantId, options) {
    return this.fetch(`/assistants/${assistantId}/graph`, {
      params: { xray: options?.xray }
    });
  }
  /**
   * Get the state and config schema of the graph assigned to a runnable
   * @param assistantId The ID of the assistant.
   * @returns Graph schema
   */
  async getSchemas(assistantId) {
    return this.fetch(`/assistants/${assistantId}/schemas`);
  }
  /**
   * Get the schemas of an assistant by ID.
   *
   * @param assistantId The ID of the assistant to get the schema of.
   * @param options Additional options for getting subgraphs, such as namespace or recursion extraction.
   * @returns The subgraphs of the assistant.
   */
  async getSubgraphs(assistantId, options) {
    if (options?.namespace) {
      return this.fetch(`/assistants/${assistantId}/subgraphs/${options.namespace}`, { params: { recurse: options?.recurse } });
    }
    return this.fetch(`/assistants/${assistantId}/subgraphs`, {
      params: { recurse: options?.recurse }
    });
  }
  /**
   * Create a new assistant.
   * @param payload Payload for creating an assistant.
   * @returns The created assistant.
   */
  async create(payload) {
    return this.fetch("/assistants", {
      method: "POST",
      json: {
        graph_id: payload.graphId,
        config: payload.config,
        metadata: payload.metadata,
        assistant_id: payload.assistantId,
        if_exists: payload.ifExists,
        name: payload.name,
        description: payload.description
      }
    });
  }
  /**
   * Update an assistant.
   * @param assistantId ID of the assistant.
   * @param payload Payload for updating the assistant.
   * @returns The updated assistant.
   */
  async update(assistantId, payload) {
    return this.fetch(`/assistants/${assistantId}`, {
      method: "PATCH",
      json: {
        graph_id: payload.graphId,
        config: payload.config,
        metadata: payload.metadata,
        name: payload.name,
        description: payload.description
      }
    });
  }
  /**
   * Delete an assistant.
   *
   * @param assistantId ID of the assistant.
   */
  async delete(assistantId) {
    return this.fetch(`/assistants/${assistantId}`, {
      method: "DELETE"
    });
  }
  /**
   * List assistants.
   * @param query Query options.
   * @returns List of assistants.
   */
  async search(query) {
    return this.fetch("/assistants/search", {
      method: "POST",
      json: {
        graph_id: query?.graphId ?? void 0,
        metadata: query?.metadata ?? void 0,
        limit: query?.limit ?? 10,
        offset: query?.offset ?? 0,
        sort_by: query?.sortBy ?? void 0,
        sort_order: query?.sortOrder ?? void 0
      }
    });
  }
  /**
   * List all versions of an assistant.
   *
   * @param assistantId ID of the assistant.
   * @returns List of assistant versions.
   */
  async getVersions(assistantId, payload) {
    return this.fetch(`/assistants/${assistantId}/versions`, {
      method: "POST",
      json: {
        metadata: payload?.metadata ?? void 0,
        limit: payload?.limit ?? 10,
        offset: payload?.offset ?? 0
      }
    });
  }
  /**
   * Change the version of an assistant.
   *
   * @param assistantId ID of the assistant.
   * @param version The version to change to.
   * @returns The updated assistant.
   */
  async setLatest(assistantId, version) {
    return this.fetch(`/assistants/${assistantId}/latest`, {
      method: "POST",
      json: { version }
    });
  }
};
var ThreadsClient = class extends BaseClient {
  /**
   * Get a thread by ID.
   *
   * @param threadId ID of the thread.
   * @returns The thread.
   */
  async get(threadId) {
    return this.fetch(`/threads/${threadId}`);
  }
  /**
   * Create a new thread.
   *
   * @param payload Payload for creating a thread.
   * @returns The created thread.
   */
  async create(payload) {
    return this.fetch(`/threads`, {
      method: "POST",
      json: {
        metadata: __spreadProps(__spreadValues({}, payload?.metadata), {
          graph_id: payload?.graphId
        }),
        thread_id: payload?.threadId,
        if_exists: payload?.ifExists,
        supersteps: payload?.supersteps?.map((s) => ({
          updates: s.updates.map((u) => ({
            values: u.values,
            command: u.command,
            as_node: u.asNode
          }))
        }))
      }
    });
  }
  /**
   * Copy an existing thread
   * @param threadId ID of the thread to be copied
   * @returns Newly copied thread
   */
  async copy(threadId) {
    return this.fetch(`/threads/${threadId}/copy`, {
      method: "POST"
    });
  }
  /**
   * Update a thread.
   *
   * @param threadId ID of the thread.
   * @param payload Payload for updating the thread.
   * @returns The updated thread.
   */
  async update(threadId, payload) {
    return this.fetch(`/threads/${threadId}`, {
      method: "PATCH",
      json: { metadata: payload?.metadata }
    });
  }
  /**
   * Delete a thread.
   *
   * @param threadId ID of the thread.
   */
  async delete(threadId) {
    return this.fetch(`/threads/${threadId}`, {
      method: "DELETE"
    });
  }
  /**
   * List threads
   *
   * @param query Query options
   * @returns List of threads
   */
  async search(query) {
    return this.fetch("/threads/search", {
      method: "POST",
      json: {
        metadata: query?.metadata ?? void 0,
        limit: query?.limit ?? 10,
        offset: query?.offset ?? 0,
        status: query?.status,
        sort_by: query?.sortBy,
        sort_order: query?.sortOrder
      }
    });
  }
  /**
   * Get state for a thread.
   *
   * @param threadId ID of the thread.
   * @returns Thread state.
   */
  async getState(threadId, checkpoint, options) {
    if (checkpoint != null) {
      if (typeof checkpoint !== "string") {
        return this.fetch(`/threads/${threadId}/state/checkpoint`, {
          method: "POST",
          json: { checkpoint, subgraphs: options?.subgraphs }
        });
      }
      return this.fetch(`/threads/${threadId}/state/${checkpoint}`, { params: { subgraphs: options?.subgraphs } });
    }
    return this.fetch(`/threads/${threadId}/state`, {
      params: { subgraphs: options?.subgraphs }
    });
  }
  /**
   * Add state to a thread.
   *
   * @param threadId The ID of the thread.
   * @returns
   */
  async updateState(threadId, options) {
    return this.fetch(`/threads/${threadId}/state`, {
      method: "POST",
      json: {
        values: options.values,
        checkpoint_id: options.checkpointId,
        checkpoint: options.checkpoint,
        as_node: options?.asNode
      }
    });
  }
  /**
   * Patch the metadata of a thread.
   *
   * @param threadIdOrConfig Thread ID or config to patch the state of.
   * @param metadata Metadata to patch the state with.
   */
  async patchState(threadIdOrConfig, metadata) {
    let threadId;
    if (typeof threadIdOrConfig !== "string") {
      if (typeof threadIdOrConfig.configurable?.thread_id !== "string") {
        throw new Error("Thread ID is required when updating state with a config.");
      }
      threadId = threadIdOrConfig.configurable.thread_id;
    } else {
      threadId = threadIdOrConfig;
    }
    return this.fetch(`/threads/${threadId}/state`, {
      method: "PATCH",
      json: { metadata }
    });
  }
  /**
   * Get all past states for a thread.
   *
   * @param threadId ID of the thread.
   * @param options Additional options.
   * @returns List of thread states.
   */
  async getHistory(threadId, options) {
    return this.fetch(`/threads/${threadId}/history`, {
      method: "POST",
      json: {
        limit: options?.limit ?? 10,
        before: options?.before,
        metadata: options?.metadata,
        checkpoint: options?.checkpoint
      }
    });
  }
};
var RunsClient = class extends BaseClient {
  /**
   * Create a run and stream the results.
   *
   * @param threadId The ID of the thread.
   * @param assistantId Assistant ID to use for this run.
   * @param payload Payload for creating a run.
   */
  async *stream(threadId, assistantId, payload) {
    const json = {
      input: payload?.input,
      command: payload?.command,
      config: payload?.config,
      metadata: payload?.metadata,
      stream_mode: payload?.streamMode,
      stream_subgraphs: payload?.streamSubgraphs,
      stream_resumable: payload?.streamResumable,
      feedback_keys: payload?.feedbackKeys,
      assistant_id: assistantId,
      interrupt_before: payload?.interruptBefore,
      interrupt_after: payload?.interruptAfter,
      checkpoint: payload?.checkpoint,
      checkpoint_id: payload?.checkpointId,
      webhook: payload?.webhook,
      multitask_strategy: payload?.multitaskStrategy,
      on_completion: payload?.onCompletion,
      on_disconnect: payload?.onDisconnect,
      after_seconds: payload?.afterSeconds,
      if_not_exists: payload?.ifNotExists,
      checkpoint_during: payload?.checkpointDuring
    };
    const endpoint = threadId == null ? `/runs/stream` : `/threads/${threadId}/runs/stream`;
    const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(endpoint, {
      method: "POST",
      json,
      timeoutMs: null,
      signal: payload?.signal
    }));
    const runMetadata = getRunMetadataFromResponse(response);
    if (runMetadata)
      payload?.onRunCreated?.(runMetadata);
    const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() })).pipeThrough(new BytesLineDecoder()).pipeThrough(new SSEDecoder());
    yield* IterableReadableStream.fromReadableStream(stream);
  }
  /**
   * Create a run.
   *
   * @param threadId The ID of the thread.
   * @param assistantId Assistant ID to use for this run.
   * @param payload Payload for creating a run.
   * @returns The created run.
   */
  async create(threadId, assistantId, payload) {
    const json = {
      input: payload?.input,
      command: payload?.command,
      config: payload?.config,
      metadata: payload?.metadata,
      stream_mode: payload?.streamMode,
      stream_subgraphs: payload?.streamSubgraphs,
      stream_resumable: payload?.streamResumable,
      assistant_id: assistantId,
      interrupt_before: payload?.interruptBefore,
      interrupt_after: payload?.interruptAfter,
      webhook: payload?.webhook,
      checkpoint: payload?.checkpoint,
      checkpoint_id: payload?.checkpointId,
      multitask_strategy: payload?.multitaskStrategy,
      after_seconds: payload?.afterSeconds,
      if_not_exists: payload?.ifNotExists,
      checkpoint_during: payload?.checkpointDuring,
      langsmith_tracer: payload?._langsmithTracer ? {
        project_name: payload?._langsmithTracer?.projectName,
        example_id: payload?._langsmithTracer?.exampleId
      } : void 0
    };
    const [run, response] = await this.fetch(`/threads/${threadId}/runs`, {
      method: "POST",
      json,
      signal: payload?.signal,
      withResponse: true
    });
    const runMetadata = getRunMetadataFromResponse(response);
    if (runMetadata)
      payload?.onRunCreated?.(runMetadata);
    return run;
  }
  /**
   * Create a batch of stateless background runs.
   *
   * @param payloads An array of payloads for creating runs.
   * @returns An array of created runs.
   */
  async createBatch(payloads) {
    const filteredPayloads = payloads.map((payload) => __spreadProps(__spreadValues({}, payload), { assistant_id: payload.assistantId })).map((payload) => {
      return Object.fromEntries(Object.entries(payload).filter(([_, v]) => v !== void 0));
    });
    return this.fetch("/runs/batch", {
      method: "POST",
      json: filteredPayloads
    });
  }
  /**
   * Create a run and wait for it to complete.
   *
   * @param threadId The ID of the thread.
   * @param assistantId Assistant ID to use for this run.
   * @param payload Payload for creating a run.
   * @returns The last values chunk of the thread.
   */
  async wait(threadId, assistantId, payload) {
    const json = {
      input: payload?.input,
      command: payload?.command,
      config: payload?.config,
      metadata: payload?.metadata,
      assistant_id: assistantId,
      interrupt_before: payload?.interruptBefore,
      interrupt_after: payload?.interruptAfter,
      checkpoint: payload?.checkpoint,
      checkpoint_id: payload?.checkpointId,
      webhook: payload?.webhook,
      multitask_strategy: payload?.multitaskStrategy,
      on_completion: payload?.onCompletion,
      on_disconnect: payload?.onDisconnect,
      after_seconds: payload?.afterSeconds,
      if_not_exists: payload?.ifNotExists,
      checkpoint_during: payload?.checkpointDuring,
      langsmith_tracer: payload?._langsmithTracer ? {
        project_name: payload?._langsmithTracer?.projectName,
        example_id: payload?._langsmithTracer?.exampleId
      } : void 0
    };
    const endpoint = threadId == null ? `/runs/wait` : `/threads/${threadId}/runs/wait`;
    const [run, response] = await this.fetch(endpoint, {
      method: "POST",
      json,
      timeoutMs: null,
      signal: payload?.signal,
      withResponse: true
    });
    const runMetadata = getRunMetadataFromResponse(response);
    if (runMetadata)
      payload?.onRunCreated?.(runMetadata);
    const raiseError = payload?.raiseError !== void 0 ? payload.raiseError : true;
    if (raiseError && "__error__" in run && typeof run.__error__ === "object" && run.__error__ && "error" in run.__error__ && "message" in run.__error__) {
      throw new Error(`${run.__error__?.error}: ${run.__error__?.message}`);
    }
    return run;
  }
  /**
   * List all runs for a thread.
   *
   * @param threadId The ID of the thread.
   * @param options Filtering and pagination options.
   * @returns List of runs.
   */
  async list(threadId, options) {
    return this.fetch(`/threads/${threadId}/runs`, {
      params: {
        limit: options?.limit ?? 10,
        offset: options?.offset ?? 0,
        status: options?.status ?? void 0
      }
    });
  }
  /**
   * Get a run by ID.
   *
   * @param threadId The ID of the thread.
   * @param runId The ID of the run.
   * @returns The run.
   */
  async get(threadId, runId) {
    return this.fetch(`/threads/${threadId}/runs/${runId}`);
  }
  /**
   * Cancel a run.
   *
   * @param threadId The ID of the thread.
   * @param runId The ID of the run.
   * @param wait Whether to block when canceling
   * @param action Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. Default is `interrupt`.
   * @returns
   */
  async cancel(threadId, runId, wait = false, action = "interrupt") {
    return this.fetch(`/threads/${threadId}/runs/${runId}/cancel`, {
      method: "POST",
      params: {
        wait: wait ? "1" : "0",
        action
      }
    });
  }
  /**
   * Block until a run is done.
   *
   * @param threadId The ID of the thread.
   * @param runId The ID of the run.
   * @returns
   */
  async join(threadId, runId, options) {
    return this.fetch(`/threads/${threadId}/runs/${runId}/join`, {
      timeoutMs: null,
      signal: options?.signal
    });
  }
  /**
   * Stream output from a run in real-time, until the run is done.
   *
   * @param threadId The ID of the thread. Can be set to `null` | `undefined` for stateless runs.
   * @param runId The ID of the run.
   * @param options Additional options for controlling the stream behavior:
   *   - signal: An AbortSignal that can be used to cancel the stream request
   *   - lastEventId: The ID of the last event received. Can be used to reconnect to a stream without losing events.
   *   - cancelOnDisconnect: When true, automatically cancels the run if the client disconnects from the stream
   *   - streamMode: Controls what types of events to receive from the stream (can be a single mode or array of modes)
   *        Must be a subset of the stream modes passed when creating the run. Background runs default to having the union of all
   *        stream modes enabled.
   * @returns An async generator yielding stream parts.
   */
  async *joinStream(threadId, runId, options) {
    const opts = typeof options === "object" && options != null && options instanceof AbortSignal ? { signal: options } : options;
    const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(threadId != null ? `/threads/${threadId}/runs/${runId}/stream` : `/runs/${runId}/stream`, {
      method: "GET",
      timeoutMs: null,
      signal: opts?.signal,
      headers: opts?.lastEventId ? { "Last-Event-ID": opts.lastEventId } : void 0,
      params: {
        cancel_on_disconnect: opts?.cancelOnDisconnect ? "1" : "0",
        stream_mode: opts?.streamMode
      }
    }));
    const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() })).pipeThrough(new BytesLineDecoder()).pipeThrough(new SSEDecoder());
    yield* IterableReadableStream.fromReadableStream(stream);
  }
  /**
   * Delete a run.
   *
   * @param threadId The ID of the thread.
   * @param runId The ID of the run.
   * @returns
   */
  async delete(threadId, runId) {
    return this.fetch(`/threads/${threadId}/runs/${runId}`, {
      method: "DELETE"
    });
  }
};
var StoreClient = class extends BaseClient {
  /**
   * Store or update an item.
   *
   * @param namespace A list of strings representing the namespace path.
   * @param key The unique identifier for the item within the namespace.
   * @param value A dictionary containing the item's data.
   * @param options.index Controls search indexing - null (use defaults), false (disable), or list of field paths to index.
   * @param options.ttl Optional time-to-live in minutes for the item, or null for no expiration.
   * @returns Promise<void>
   *
   * @example
   * ```typescript
   * await client.store.putItem(
   *   ["documents", "user123"],
   *   "item456",
   *   { title: "My Document", content: "Hello World" },
   *   { ttl: 60 } // expires in 60 minutes
   * );
   * ```
   */
  async putItem(namespace, key, value, options) {
    namespace.forEach((label) => {
      if (label.includes(".")) {
        throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);
      }
    });
    const payload = {
      namespace,
      key,
      value,
      index: options?.index,
      ttl: options?.ttl
    };
    return this.fetch("/store/items", {
      method: "PUT",
      json: payload
    });
  }
  /**
   * Retrieve a single item.
   *
   * @param namespace A list of strings representing the namespace path.
   * @param key The unique identifier for the item.
   * @param options.refreshTtl Whether to refresh the TTL on this read operation. If null, uses the store's default behavior.
   * @returns Promise<Item>
   *
   * @example
   * ```typescript
   * const item = await client.store.getItem(
   *   ["documents", "user123"],
   *   "item456",
   *   { refreshTtl: true }
   * );
   * console.log(item);
   * // {
   * //   namespace: ["documents", "user123"],
   * //   key: "item456",
   * //   value: { title: "My Document", content: "Hello World" },
   * //   createdAt: "2024-07-30T12:00:00Z",
   * //   updatedAt: "2024-07-30T12:00:00Z"
   * // }
   * ```
   */
  async getItem(namespace, key, options) {
    namespace.forEach((label) => {
      if (label.includes(".")) {
        throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);
      }
    });
    const params = {
      namespace: namespace.join("."),
      key
    };
    if (options?.refreshTtl !== void 0) {
      params.refresh_ttl = options.refreshTtl;
    }
    const response = await this.fetch("/store/items", {
      params
    });
    return response ? __spreadProps(__spreadValues({}, response), {
      createdAt: response.created_at,
      updatedAt: response.updated_at
    }) : null;
  }
  /**
   * Delete an item.
   *
   * @param namespace A list of strings representing the namespace path.
   * @param key The unique identifier for the item.
   * @returns Promise<void>
   */
  async deleteItem(namespace, key) {
    namespace.forEach((label) => {
      if (label.includes(".")) {
        throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);
      }
    });
    return this.fetch("/store/items", {
      method: "DELETE",
      json: { namespace, key }
    });
  }
  /**
   * Search for items within a namespace prefix.
   *
   * @param namespacePrefix List of strings representing the namespace prefix.
   * @param options.filter Optional dictionary of key-value pairs to filter results.
   * @param options.limit Maximum number of items to return (default is 10).
   * @param options.offset Number of items to skip before returning results (default is 0).
   * @param options.query Optional search query.
   * @param options.refreshTtl Whether to refresh the TTL on items returned by this search. If null, uses the store's default behavior.
   * @returns Promise<SearchItemsResponse>
   *
   * @example
   * ```typescript
   * const results = await client.store.searchItems(
   *   ["documents"],
   *   {
   *     filter: { author: "John Doe" },
   *     limit: 5,
   *     refreshTtl: true
   *   }
   * );
   * console.log(results);
   * // {
   * //   items: [
   * //     {
   * //       namespace: ["documents", "user123"],
   * //       key: "item789",
   * //       value: { title: "Another Document", author: "John Doe" },
   * //       createdAt: "2024-07-30T12:00:00Z",
   * //       updatedAt: "2024-07-30T12:00:00Z"
   * //     },
   * //     // ... additional items ...
   * //   ]
   * // }
   * ```
   */
  async searchItems(namespacePrefix, options) {
    const payload = {
      namespace_prefix: namespacePrefix,
      filter: options?.filter,
      limit: options?.limit ?? 10,
      offset: options?.offset ?? 0,
      query: options?.query,
      refresh_ttl: options?.refreshTtl
    };
    const response = await this.fetch("/store/items/search", {
      method: "POST",
      json: payload
    });
    return {
      items: response.items.map((item) => __spreadProps(__spreadValues({}, item), {
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }))
    };
  }
  /**
   * List namespaces with optional match conditions.
   *
   * @param options.prefix Optional list of strings representing the prefix to filter namespaces.
   * @param options.suffix Optional list of strings representing the suffix to filter namespaces.
   * @param options.maxDepth Optional integer specifying the maximum depth of namespaces to return.
   * @param options.limit Maximum number of namespaces to return (default is 100).
   * @param options.offset Number of namespaces to skip before returning results (default is 0).
   * @returns Promise<ListNamespaceResponse>
   */
  async listNamespaces(options) {
    const payload = {
      prefix: options?.prefix,
      suffix: options?.suffix,
      max_depth: options?.maxDepth,
      limit: options?.limit ?? 100,
      offset: options?.offset ?? 0
    };
    return this.fetch("/store/namespaces", {
      method: "POST",
      json: payload
    });
  }
};
var UiClient = class _UiClient extends BaseClient {
  static getOrCached(key, fn) {
    if (_UiClient.promiseCache[key] != null) {
      return _UiClient.promiseCache[key];
    }
    const promise = fn();
    _UiClient.promiseCache[key] = promise;
    return promise;
  }
  async getComponent(assistantId, agentName) {
    return _UiClient["getOrCached"](`${this.apiUrl}-${assistantId}-${agentName}`, async () => {
      const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/ui/${assistantId}`, {
        headers: {
          Accept: "text/html",
          "Content-Type": "application/json"
        },
        method: "POST",
        json: { name: agentName }
      }));
      return response.text();
    });
  }
};
Object.defineProperty(UiClient, "promiseCache", {
  enumerable: true,
  configurable: true,
  writable: true,
  value: {}
});
var Client = class {
  constructor(config) {
    Object.defineProperty(this, "assistants", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "threads", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "runs", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "crons", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "store", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, "~ui", {
      enumerable: true,
      configurable: true,
      writable: true,
      value: void 0
    });
    this.assistants = new AssistantsClient(config);
    this.threads = new ThreadsClient(config);
    this.runs = new RunsClient(config);
    this.crons = new CronsClient(config);
    this.store = new StoreClient(config);
    this["~ui"] = new UiClient(config);
  }
};
export {
  Client,
  getApiKey,
  overrideFetchImplementation
};
//# sourceMappingURL=@langchain_langgraph-sdk.js.map
