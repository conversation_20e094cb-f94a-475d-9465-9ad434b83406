# NestJS + Fastify + LangGraph Backend

This is a TypeScript implementation of the original Python FastAPI + LangGraph backend, migrated to use NestJS with Fastify adapter.

## Project Structure

```
backend-ts/
├── src/
│   ├── agent/                 # Agent module with LangGraph implementation
│   │   ├── agent.controller.ts    # API endpoints
│   │   ├── agent.module.ts        # Agent module definition
│   │   ├── agent.service.ts       # Core agent logic and LangGraph workflow
│   │   ├── configuration.service.ts # Agent configuration
│   │   ├── prompts.ts             # Prompt templates
│   │   ├── state.ts               # State interfaces
│   │   ├── tools-and-schemas.ts   # Data models and schemas
│   │   ├── types.ts               # Type definitions
│   │   └── utils.ts               # Utility functions
│   ├── config/                # Configuration module
│   │   └── config.module.ts       # Environment configuration
│   ├── app.module.ts          # Root application module
│   └── main.ts               # Application entry point
├── .env.example              # Example environment variables
├── package.json              # Dependencies and scripts
└── tsconfig.json             # TypeScript configuration
```

## Setup Instructions

1. Clone the repository
2. Navigate to the backend-ts directory
3. Copy `.env.example` to `.env` and add your Gemini API key
4. Install dependencies:
   ```
   pnpm install
   ```
5. Start the development server:
   ```
   pnpm start:dev
   ```

## API Endpoints

- `POST /api/run` - Run the agent with a set of messages

## Environment Variables

- `GEMINI_API_KEY` - Google Gemini API key
- `QUERY_GENERATOR_MODEL` - Model for generating search queries (default: gemini-2.0-flash)
- `REFLECTION_MODEL` - Model for reflection (default: gemini-2.5-flash-preview-04-17)
- `ANSWER_MODEL` - Model for generating final answers (default: gemini-2.5-pro-preview-05-06)
- `NUMBER_OF_INITIAL_QUERIES` - Number of initial search queries (default: 3)
- `MAX_RESEARCH_LOOPS` - Maximum number of research loops (default: 2)

## Migration Notes

This project is a strict lossless migration of the original Python FastAPI + LangGraph backend to TypeScript using NestJS with Fastify. All functionality, API endpoints, data structures, and business logic have been preserved.

Key components migrated:
- LangGraph agent workflows and state management
- Agent configuration and environment variables
- Prompt templates and utility functions
- Data models and validation schemas
