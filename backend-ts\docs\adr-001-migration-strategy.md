# Architectural Decision Record: FastAPI to NestJS Migration Strategy

## Context

The project requires migrating a Python backend built with FastAPI and LangGraph to a TypeScript backend using NestJS with Fastify adapter and LangGraph. The migration must be strictly lossless, preserving all functionality, API endpoints, data structures, and business logic.

## Decision

We will implement a feature-by-feature migration approach, focusing on maintaining functional equivalence rather than code structure equivalence. The migration will use TypeScript with strict mode and follow NestJS best practices.

### Key Migration Decisions

1. **Framework Selection**
   - NestJS with Fastify adapter (not Express)
   - TypeScript with strict mode enabled
   - pnpm as package manager

2. **Project Structure**
   - Modular architecture following NestJS conventions
   - Separation of concerns: controllers, services, modules
   - Agent logic encapsulated in dedicated module

3. **State Management**
   - Direct port of Python TypedDict classes to TypeScript interfaces
   - Preservation of all state transitions and data flow

4. **LangGraph Implementation**
   - Equivalent implementation of agent nodes and workflows
   - Preservation of all prompt templates and utility functions
   - Placeholder for Google Generative AI client integration

5. **API Endpoints**
   - Maintain API compatibility with existing frontend
   - Implement equivalent request/response schemas

6. **Configuration Management**
   - Environment variables preserved with same names and defaults
   - NestJS ConfigModule for centralized configuration

## Consequences

### Positive

- Maintainable codebase following TypeScript and NestJS best practices
- Strict typing improves code quality and developer experience
- Modular architecture facilitates future extensions
- Preserved API compatibility ensures seamless frontend integration

### Negative

- Some TypeScript-specific adaptations required for Python patterns
- LangGraph TypeScript implementation may differ slightly from Python version
- Initial setup complexity due to TypeScript strict mode

### Neutral

- Different project structure compared to original Python codebase
- Different error handling patterns between FastAPI and NestJS
