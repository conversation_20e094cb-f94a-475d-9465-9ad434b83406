import { Component, inject, signal, computed, effect, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LangGraphService } from './services/langgraph.service';
import { ResearchConfig } from './models/message.model';
import { WelcomeScreen } from './components/welcome-screen/welcome-screen';
import { ChatMessagesView } from './components/chat-messages-view/chat-messages-view';

@Component({
  selector: 'app-root',
  imports: [CommonModule, WelcomeScreen, ChatMessagesView],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements AfterViewInit {
  private langGraphService = inject(LangGraphService);

  @ViewChild('scrollContainer') scrollContainer?: ElementRef<HTMLDivElement>;

  // Expose service signals to template
  readonly messages = this.langGraphService.messages;
  readonly isLoading = this.langGraphService.isLoading;
  readonly hasMessages = this.langGraphService.hasMessages;
  readonly processedEventsTimeline = this.langGraphService.processedEventsTimeline;
  readonly historicalActivities = this.langGraphService.historicalActivities;

  constructor() {
    // Auto-scroll effect when messages change
    effect(() => {
      const msgs = this.messages();
      if (msgs.length > 0) {
        setTimeout(() => this.scrollToBottom(), 100);
      }
    });
  }

  ngAfterViewInit(): void {
    this.scrollToBottom();
  }

  private scrollToBottom(): void {
    if (this.scrollContainer?.nativeElement) {
      const element = this.scrollContainer.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  onSubmit(inputValue: string, config: ResearchConfig): void {
    this.langGraphService.submitQuery(inputValue, config);
  }

  onCancel(): void {
    this.langGraphService.stopProcessing();
  }

  onNewSearch(): void {
    this.langGraphService.resetChat();
  }
}
