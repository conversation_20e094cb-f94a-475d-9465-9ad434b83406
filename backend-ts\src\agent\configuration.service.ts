import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Configuration service for the agent.
 * Equivalent to the Python Configuration class.
 */
@Injectable()
export class ConfigurationService {
  constructor(private configService: ConfigService) {}

  /**
   * The name of the language model to use for the agent's query generation.
   */
  get queryGeneratorModel(): string {
    return this.configService.get<string>('QUERY_GENERATOR_MODEL', 'gemini-2.0-flash');
  }

  /**
   * The name of the language model to use for the agent's reflection.
   */
  get reflectionModel(): string {
    return this.configService.get<string>('REFLECTION_MODEL', 'gemini-2.5-flash-preview-04-17');
  }

  /**
   * The name of the language model to use for the agent's answer.
   */
  get answerModel(): string {
    return this.configService.get<string>('ANSWER_MODEL', 'gemini-2.5-pro-preview-05-06');
  }

  /**
   * The number of initial search queries to generate.
   */
  get numberOfInitialQueries(): number {
    return this.configService.get<number>('NUMBER_OF_INITIAL_QUERIES', 3);
  }

  /**
   * The maximum number of research loops to perform.
   */
  get maxResearchLoops(): number {
    return this.configService.get<number>('MAX_RESEARCH_LOOPS', 2);
  }

  /**
   * Create a Configuration instance from a config object.
   * This is a utility method to maintain compatibility with the Python implementation.
   */
  static fromRunnableConfig(config: Record<string, any> | null = null): Record<string, any> {
    const configurable = config && 'configurable' in config ? config.configurable : {};
    
    // Get environment variables or use defaults
    const values: Record<string, any> = {
      queryGeneratorModel: process.env.QUERY_GENERATOR_MODEL || configurable.queryGeneratorModel || 'gemini-2.0-flash',
      reflectionModel: process.env.REFLECTION_MODEL || configurable.reflectionModel || 'gemini-2.5-flash-preview-04-17',
      answerModel: process.env.ANSWER_MODEL || configurable.answerModel || 'gemini-2.5-pro-preview-05-06',
      numberOfInitialQueries: parseInt(process.env.NUMBER_OF_INITIAL_QUERIES || configurable.numberOfInitialQueries || '3', 10),
      maxResearchLoops: parseInt(process.env.MAX_RESEARCH_LOOPS || configurable.maxResearchLoops || '2', 10),
    };

    return values;
  }
}
