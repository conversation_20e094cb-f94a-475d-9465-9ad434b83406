{"name": "backend-ts", "version": "0.0.1", "description": "NestJS with Fastify + LangGraph TypeScript implementation", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@langchain/core": "^0.3.28", "@langchain/google-genai": "^0.1.4", "@langchain/langgraph": "^0.3.1", "@langchain/langgraph-sdk": "^0.0.84", "@nestjs/common": "^10.4.4", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.4", "@nestjs/platform-fastify": "^10.4.4", "@nestjs/websockets": "^10.4.4", "@nestjs/platform-socket.io": "^10.4.4", "dotenv": "^16.4.7", "fastify": "^5.2.0", "@google/generative-ai": "^0.21.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "uuid": "^11.0.3", "zod": "^3.24.1"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.4", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}