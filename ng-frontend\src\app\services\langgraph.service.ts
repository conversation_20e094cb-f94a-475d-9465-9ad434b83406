import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Client } from '@langchain/langgraph-sdk';
import { Message, ProcessedEvent, ThreadState, LangGraphEvent, ResearchConfig } from '../models/message.model';
import { EnvironmentService } from './environment';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class LangGraphService {
  private http = inject(HttpClient);
  private environmentService = inject(EnvironmentService);
  private destroy$ = new Subject<void>();
  private langGraphClient: Client;
  private currentThreadId: string | null = null;

  // Signals for reactive state management
  private _messages = signal<Message[]>([]);
  private _isLoading = signal<boolean>(false);
  private _processedEventsTimeline = signal<ProcessedEvent[]>([]);
  private _historicalActivities = signal<Record<string, ProcessedEvent[]>>({});
  private _hasFinalizeEventOccurred = signal<boolean>(false);

  // Public readonly signals
  readonly messages = this._messages.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly processedEventsTimeline = this._processedEventsTimeline.asReadonly();
  readonly historicalActivities = this._historicalActivities.asReadonly();

  // Computed signals
  readonly hasMessages = computed(() => this.messages().length > 0);
  readonly lastMessage = computed(() => {
    const msgs = this.messages();
    return msgs.length > 0 ? msgs[msgs.length - 1] : null;
  });

  private apiUrl: string;

  constructor() {
    // Initialize API URL and LangGraph client
    this.apiUrl = this.environmentService.getApiUrl();
    this.langGraphClient = new Client({
      apiUrl: this.apiUrl
    });

    // Effect to handle finalization and store historical activities
    effect(() => {
      if (this._hasFinalizeEventOccurred() &&
          !this.isLoading() &&
          this.messages().length > 0) {

        const lastMsg = this.lastMessage();
        if (lastMsg && lastMsg.type === 'ai' && lastMsg.id) {
          this._historicalActivities.update(prev => ({
            ...prev,
            [lastMsg.id!]: [...this.processedEventsTimeline()]
          }));
        }
        this._hasFinalizeEventOccurred.set(false);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private processEvent(event: LangGraphEvent): ProcessedEvent | null {
    if (event.generate_query) {
      return {
        title: 'Generating Search Queries',
        data: event.generate_query.query_list.join(', '),
        timestamp: new Date()
      };
    } else if (event.web_research) {
      const sources = event.web_research.sources_gathered || [];
      const numSources = sources.length;
      const uniqueLabels = [...new Set(sources.map(s => s.label).filter(Boolean))];
      const exampleLabels = uniqueLabels.slice(0, 3).join(', ');
      return {
        title: 'Web Research',
        data: `Gathered ${numSources} sources. Related to: ${exampleLabels || 'N/A'}.`,
        timestamp: new Date()
      };
    } else if (event.reflection) {
      return {
        title: 'Reflection',
        data: event.reflection.is_sufficient
          ? 'Search successful, generating final answer.'
          : `Need more information, searching for ${event.reflection.follow_up_queries.join(', ')}`,
        timestamp: new Date()
      };
    } else if (event.finalize_answer) {
      this._hasFinalizeEventOccurred.set(true);
      return {
        title: 'Finalizing Answer',
        data: 'Composing and presenting the final answer.',
        timestamp: new Date()
      };
    }
    return null;
  }

  private getResearchParams(effort: string): { initial_search_query_count: number; max_research_loops: number } {
    switch (effort) {
      case 'low':
        return { initial_search_query_count: 1, max_research_loops: 1 };
      case 'medium':
        return { initial_search_query_count: 3, max_research_loops: 3 };
      case 'high':
        return { initial_search_query_count: 5, max_research_loops: 10 };
      default:
        return { initial_search_query_count: 3, max_research_loops: 3 };
    }
  }

  async submitQuery(inputValue: string, config: ResearchConfig): Promise<void> {
    if (!inputValue.trim()) return;

    // Reset timeline for new query
    this._processedEventsTimeline.set([]);
    this._hasFinalizeEventOccurred.set(false);

    const params = this.getResearchParams(config.effort);

    const newMessage: Message = {
      type: 'human',
      content: inputValue,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    // Add human message
    this._messages.update(prev => [...prev, newMessage]);
    this._isLoading.set(true);

    try {
      // Create or get thread
      if (!this.currentThreadId) {
        const thread = await this.langGraphClient.threads.create();
        this.currentThreadId = thread.thread_id;
      }

      // Prepare thread state
      const threadState: ThreadState = {
        messages: [...this.messages()],
        initial_search_query_count: params.initial_search_query_count,
        max_research_loops: params.max_research_loops,
        reasoning_model: config.model
      };

      // Start streaming
      const stream = this.langGraphClient.runs.stream(
        this.currentThreadId,
        'agent',
        {
          input: threadState,
          streamMode: 'events'
        }
      );

      // Process stream events
      for await (const event of stream) {
        this.handleStreamEvent(event);
      }

    } catch (error) {
      console.error('LangGraph streaming error:', error);
      this._isLoading.set(false);
      // Fallback to simulation for development
      this.simulateLangGraphStream(inputValue, params, config.model);
    }
  }

  private handleStreamEvent(event: any): void {
    // Handle different types of stream events
    if (event.event === 'on_chain_stream' && event.data) {
      const processedEvent = this.processEvent(event.data);
      if (processedEvent) {
        this._processedEventsTimeline.update(prev => [...prev, processedEvent]);
      }
    } else if (event.event === 'on_chain_end' && event.data?.output) {
      // Handle final message
      const output = event.data.output;
      if (output.messages && output.messages.length > 0) {
        const lastMessage = output.messages[output.messages.length - 1];
        if (lastMessage.type === 'ai') {
          const aiMessage: Message = {
            type: 'ai',
            content: lastMessage.content,
            id: Date.now().toString(),
            timestamp: new Date()
          };
          this._messages.update(prev => [...prev, aiMessage]);
        }
      }
      this._isLoading.set(false);
    }
  }

  private simulateLangGraphStream(query: string, params: any, model: string): void {
    // This is a simulation - in real implementation, we'll use the LangGraph SDK
    // For now, let's simulate the process

    setTimeout(() => {
      // Simulate generate_query event
      const generateEvent = this.processEvent({
        generate_query: { query_list: [`Research about: ${query}`] }
      });
      if (generateEvent) {
        this._processedEventsTimeline.update(prev => [...prev, generateEvent]);
      }
    }, 500);

    setTimeout(() => {
      // Simulate web_research event
      const researchEvent = this.processEvent({
        web_research: {
          sources_gathered: [
            { label: 'Wikipedia', url: 'https://wikipedia.org' },
            { label: 'News Source', url: 'https://news.com' }
          ]
        }
      });
      if (researchEvent) {
        this._processedEventsTimeline.update(prev => [...prev, researchEvent]);
      }
    }, 1500);

    setTimeout(() => {
      // Simulate reflection event
      const reflectionEvent = this.processEvent({
        reflection: { is_sufficient: true, follow_up_queries: [] }
      });
      if (reflectionEvent) {
        this._processedEventsTimeline.update(prev => [...prev, reflectionEvent]);
      }
    }, 2500);

    setTimeout(() => {
      // Simulate finalize_answer event
      const finalizeEvent = this.processEvent({
        finalize_answer: { final_answer: 'Generated response' }
      });
      if (finalizeEvent) {
        this._processedEventsTimeline.update(prev => [...prev, finalizeEvent]);
      }

      // Add AI response
      const aiMessage: Message = {
        type: 'ai',
        content: `Here's a comprehensive response to your query: "${query}". This is a simulated response that demonstrates the Angular v20 zoneless architecture with signals-based reactivity.`,
        id: (Date.now() + 1).toString(),
        timestamp: new Date()
      };

      this._messages.update(prev => [...prev, aiMessage]);
      this._isLoading.set(false);
    }, 3500);
  }

  async stopProcessing(): Promise<void> {
    this._isLoading.set(false);
    this.destroy$.next(); // Cancel any ongoing streams

    // If we have an active thread, we could potentially cancel the run
    if (this.currentThreadId) {
      try {
        // Note: LangGraph SDK may not have direct cancellation,
        // but we can stop processing on our end
        console.log('Stopping processing for thread:', this.currentThreadId);
      } catch (error) {
        console.error('Error stopping LangGraph stream:', error);
      }
    }
  }

  resetChat(): void {
    this._messages.set([]);
    this._processedEventsTimeline.set([]);
    this._historicalActivities.set({});
    this._hasFinalizeEventOccurred.set(false);
    this._isLoading.set(false);
    this.currentThreadId = null; // Reset thread
  }
}
