import { BaseMessage, HumanMessage as <PERSON>CH<PERSON>Message, AIMessage as LCAIMessage } from '@langchain/core/messages';

/**
 * Base message interface - using LangChain core types
 */
export type Message = BaseMessage;

/**
 * Human message - using LangChain core types
 */
export type HumanMessage = LCHumanMessage;

/**
 * AI message - using LangChain core types
 */
export type AIMessage = LCAIMessage;

/**
 * Any message type - using LangChain core types
 */
export type AnyMessage = BaseMessage;

/**
 * Citation segment
 */
export interface CitationSegment {
  label: string;
  short_url: string;
  value: string;
}

/**
 * Citation
 */
export interface Citation {
  start_index: number;
  end_index: number;
  segments: CitationSegment[];
}

/**
 * Runnable configuration - using LangChain core types
 */
export interface RunnableConfig {
  configurable?: Record<string, any>;
  metadata?: Record<string, any>;
  tags?: string[];
  callbacks?: any;
}

/**
 * LangGraph Send type for conditional edges
 */
export interface Send {
  node: string;
  arg: any;
}

/**
 * Google Generative AI response structure
 */
export interface GoogleGenAIResponse {
  text: string;
  candidates: Array<{
    content: {
      parts: Array<{ text: string }>;
    };
    grounding_metadata?: {
      grounding_chunks: Array<{
        web: {
          uri: string;
          title: string;
        };
      }>;
      grounding_supports: Array<{
        segment: {
          start_index: number;
          end_index: number;
        };
        grounding_chunk_indices: number[];
      }>;
    };
  }>;
}
