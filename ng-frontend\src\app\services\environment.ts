import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {

  constructor() { }

  /**
   * Get API URL based on environment - matches React version logic
   * DEV: http://localhost:2024
   * PROD: http://localhost:8123
   */
  getApiUrl(): string {
    // Check if we're in development mode (similar to import.meta.env.DEV in React)
    const isDev = typeof window !== 'undefined' &&
                  (window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.includes('localhost'));

    return isDev ? 'http://localhost:2024' : 'http://localhost:8123';
  }

  /**
   * Check if we're in development mode
   */
  isDevelopment(): boolean {
    return typeof window !== 'undefined' &&
           (window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname.includes('localhost'));
  }
}
